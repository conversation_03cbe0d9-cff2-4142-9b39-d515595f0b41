/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    font-size: 14px;
    line-height: 1.6;
}

.container {
    max-width: 320px;
    margin: 0 auto;
    padding: 16px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e0e0e0;
}

.header h1 {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 300;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section {
    background: rgba(255, 255, 255, 0.8);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section h2 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #34495e;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.btn {
    flex: 1;
    padding: 10px 12px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;
    color: white;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #1abc9c, #16a085);
    box-shadow: 0 4px 15px rgba(26, 188, 156, 0.3);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 188, 156, 0.4);
}

.btn-settings {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
    width: 100%;
}

.btn-settings:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.icon {
    font-size: 16px;
}

/* 帮助文本 */
.help-text {
    font-size: 11px;
    color: #7f8c8d;
    text-align: center;
    margin-top: 4px;
    line-height: 1.4;
}

/* 状态栏 */
.status-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
}

.status-bar {
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    text-align: center;
}

#statusText {
    font-size: 12px;
    color: #2c3e50;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #ecf0f1;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 300px;
    width: 90%;
}

.settings-content h3 {
    margin-bottom: 16px;
    color: #2c3e50;
    text-align: center;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    color: #34495e;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 响应式设计 */
@media (max-width: 360px) {
    .container {
        padding: 12px;
        margin: 8px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 8px;
    }
}
