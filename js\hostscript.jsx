// Photoshop主机脚本 - CEP扩展支持
// 提供与Photoshop应用程序的直接交互

/**
 * 获取当前活动文档
 */
function getActiveDocument() {
    try {
        if (app.documents.length > 0) {
            return app.activeDocument;
        } else {
            throw new Error("没有打开的文档");
        }
    } catch (e) {
        throw new Error("获取活动文档失败: " + e.message);
    }
}

/**
 * 获取选中的图层
 */
function getSelectedLayers() {
    try {
        var doc = getActiveDocument();
        var selectedLayers = [];
        
        if (doc.activeLayer) {
            selectedLayers.push({
                id: doc.activeLayer.id,
                name: doc.activeLayer.name,
                kind: doc.activeLayer.kind,
                bounds: {
                    left: doc.activeLayer.bounds[0].value,
                    top: doc.activeLayer.bounds[1].value,
                    right: doc.activeLayer.bounds[2].value,
                    bottom: doc.activeLayer.bounds[3].value
                }
            });
        }
        
        return JSON.stringify(selectedLayers);
    } catch (e) {
        throw new Error("获取选中图层失败: " + e.message);
    }
}

/**
 * 导出图层为文件
 */
function exportLayerToFile(layerId, filePath) {
    try {
        var doc = getActiveDocument();
        var layer = doc.layers.getByName(layerId);
        
        // 创建临时文档
        var tempDoc = app.documents.add(doc.width, doc.height, doc.resolution);
        
        // 复制图层
        layer.duplicate(tempDoc);
        
        // 导出为PNG
        var saveFile = new File(filePath);
        var pngOptions = new PNGSaveOptions();
        pngOptions.compression = 6;
        pngOptions.interlaced = false;
        
        tempDoc.saveAs(saveFile, pngOptions, true);
        tempDoc.close(SaveOptions.DONOTSAVECHANGES);
        
        return filePath;
    } catch (e) {
        throw new Error("导出图层失败: " + e.message);
    }
}

/**
 * 创建文本图层
 */
function createTextLayer(text, options) {
    try {
        var doc = getActiveDocument();
        var textLayer = doc.artLayers.add();
        textLayer.kind = LayerKind.TEXT;
        textLayer.name = options.name || "识别文字";
        
        var textItem = textLayer.textItem;
        textItem.contents = text;
        
        if (options.fontSize) {
            textItem.size = new UnitValue(options.fontSize, "px");
        }
        
        if (options.position) {
            textItem.position = [
                new UnitValue(options.position.x, "px"),
                new UnitValue(options.position.y, "px")
            ];
        }
        
        if (options.color) {
            var textColor = new SolidColor();
            textColor.rgb.red = options.color.r || 0;
            textColor.rgb.green = options.color.g || 0;
            textColor.rgb.blue = options.color.b || 0;
            textItem.color = textColor;
        }
        
        return {
            id: textLayer.id,
            name: textLayer.name
        };
    } catch (e) {
        throw new Error("创建文本图层失败: " + e.message);
    }
}

/**
 * 删除背景
 */
function removeBackground(layerId) {
    try {
        var doc = getActiveDocument();
        var layer = doc.layers.getByName(layerId);
        
        // 选择图层
        doc.activeLayer = layer;
        
        // 使用魔棒工具选择背景
        var desc = new ActionDescriptor();
        desc.putUnitDouble(charIDToTypeID("Tlrn"), charIDToTypeID("#Pxl"), 32);
        desc.putBoolean(charIDToTypeID("Cntg"), false);
        desc.putBoolean(charIDToTypeID("AntA"), true);
        executeAction(charIDToTypeID("selectColor"), desc, DialogModes.NO);
        
        // 删除选区
        executeAction(charIDToTypeID("Dlt "), undefined, DialogModes.NO);
        
        // 取消选区
        executeAction(charIDToTypeID("Dslc"), undefined, DialogModes.NO);
        
        return true;
    } catch (e) {
        throw new Error("删除背景失败: " + e.message);
    }
}

/**
 * 复制图层
 */
function duplicateLayer(layerId, newName) {
    try {
        var doc = getActiveDocument();
        var layer = doc.layers.getByName(layerId);
        var duplicatedLayer = layer.duplicate();
        
        if (newName) {
            duplicatedLayer.name = newName;
        }
        
        return {
            id: duplicatedLayer.id,
            name: duplicatedLayer.name
        };
    } catch (e) {
        throw new Error("复制图层失败: " + e.message);
    }
}

/**
 * 创建图层蒙版
 */
function createLayerMask(layerId) {
    try {
        var doc = getActiveDocument();
        var layer = doc.layers.getByName(layerId);
        
        // 选择图层
        doc.activeLayer = layer;
        
        // 创建图层蒙版
        var desc = new ActionDescriptor();
        var ref = new ActionReference();
        ref.putClass(charIDToTypeID("Chnl"));
        desc.putReference(charIDToTypeID("Nw  "), ref);
        desc.putEnumerated(charIDToTypeID("Usng"), charIDToTypeID("UsrM"), charIDToTypeID("RvlA"));
        executeAction(charIDToTypeID("Mk  "), desc, DialogModes.NO);
        
        return true;
    } catch (e) {
        throw new Error("创建图层蒙版失败: " + e.message);
    }
}

/**
 * 获取文档信息
 */
function getDocumentInfo() {
    try {
        var doc = getActiveDocument();
        return JSON.stringify({
            width: doc.width.value,
            height: doc.height.value,
            resolution: doc.resolution,
            colorMode: doc.mode,
            name: doc.name
        });
    } catch (e) {
        throw new Error("获取文档信息失败: " + e.message);
    }
}

/**
 * 显示警告对话框
 */
function showAlert(message) {
    alert(message);
    return true;
}

// 导出函数供CEP调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getActiveDocument: getActiveDocument,
        getSelectedLayers: getSelectedLayers,
        exportLayerToFile: exportLayerToFile,
        createTextLayer: createTextLayer,
        removeBackground: removeBackground,
        duplicateLayer: duplicateLayer,
        createLayerMask: createLayerMask,
        getDocumentInfo: getDocumentInfo,
        showAlert: showAlert
    };
}
