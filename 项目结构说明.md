# 图转层插件项目结构说明

## 📁 项目目录结构

```
ps图转层/
├── 📄 manifest.json              # 插件清单文件（必需）
├── 📄 index.html                 # 主界面HTML文件
├── 📄 package.json               # 项目配置文件
├── 📄 README.md                  # 项目说明文档
├── 📄 CHANGELOG.md               # 更新日志
├── 📄 项目结构说明.md            # 本文件
├── 📄 install.bat                # Windows安装脚本
│
├── 📁 css/                       # 样式文件目录
│   └── 📄 styles.css             # 主样式文件
│
├── 📁 js/                        # JavaScript文件目录
│   ├── 📄 main.js                # 主应用程序逻辑
│   ├── 📄 photoshop.js           # Photoshop API封装
│   ├── 📄 ocr.js                 # OCR文字识别模块
│   └── 📄 background-removal.js  # 背景分离模块
│
├── 📁 icons/                     # 图标文件目录
│   ├── 📄 README.md              # 图标说明文档
│   ├── 📄 icon-template.svg      # 图标模板文件
│   ├── 📄 icon-dark.png          # 深色主题图标（需要）
│   └── 📄 icon-light.png         # 浅色主题图标（需要）
│
├── 📁 config/                    # 配置文件目录
│   └── 📄 development.json       # 开发环境配置
│
├── 📁 docs/                      # 文档目录
│   └── 📄 使用教程.md            # 详细使用教程
│
└── 📁 test/                      # 测试文件目录
    └── 📁 test-images/           # 测试图片目录
        └── 📄 README.md          # 测试说明
```

## 📋 核心文件说明

### 必需文件
1. **manifest.json** - 插件清单，定义插件基本信息和权限
2. **index.html** - 插件主界面
3. **css/styles.css** - 界面样式
4. **js/main.js** - 主要业务逻辑

### 功能模块
1. **js/photoshop.js** - Photoshop API交互
2. **js/ocr.js** - OCR文字识别功能
3. **js/background-removal.js** - 背景分离功能

### 资源文件
1. **icons/** - 插件图标（23x23像素PNG格式）
2. **config/** - 配置文件
3. **docs/** - 文档和教程

## 🔧 开发环境设置

### 1. 文件权限要求
- 所有文件需要可读权限
- JavaScript文件需要执行权限
- 图标文件需要正确的格式和尺寸

### 2. Photoshop版本要求
- 最低版本：Adobe Photoshop 2023 (v24.0.0)
- 推荐版本：Adobe Photoshop 2024 或更高

### 3. 系统要求
- Windows 10/11 (64位)
- 内存：至少4GB RAM
- 硬盘：至少100MB可用空间

## 📦 安装部署

### 自动安装（推荐）
```bash
# 以管理员身份运行
install.bat
```

### 手动安装
1. 复制整个项目文件夹到Photoshop扩展目录：
   ```
   C:\Program Files\Adobe\Adobe Photoshop [版本]\Plug-ins\Extensions\图转层\
   ```
2. 重启Photoshop
3. 在菜单栏选择：窗口 > 扩展功能 > 图转层

## 🛠️ 开发指南

### 代码结构
- **模块化设计**：每个功能独立模块
- **异步处理**：避免阻塞UI线程
- **错误处理**：完善的异常捕获和用户提示
- **性能优化**：内存管理和临时文件清理

### 主要类和方法
1. **ImageToTextApp** - 主应用类
2. **PhotoshopAPI** - PS API封装类
3. **OCRService** - OCR服务类
4. **BackgroundRemovalService** - 背景分离服务类

### 事件处理
- 鼠标事件（左键/右键/中键）
- 键盘快捷键
- UI交互事件
- Photoshop文档事件

## 🔍 调试和测试

### 开发者工具
1. 打开Photoshop
2. 加载插件
3. 按F12打开开发者工具
4. 查看控制台输出和网络请求

### 测试流程
1. 准备测试图片（放在test/test-images/目录）
2. 测试各种OCR服务
3. 测试背景分离功能
4. 测试错误处理
5. 性能测试

### 常见调试问题
- **插件不显示**：检查manifest.json语法
- **功能不工作**：查看控制台错误信息
- **API调用失败**：检查网络连接和API密钥
- **性能问题**：监控内存使用和处理时间

## 📝 代码规范

### JavaScript规范
- 使用ES6+语法
- 采用async/await处理异步操作
- 详细的注释和文档
- 错误处理和日志记录

### CSS规范
- 使用现代CSS特性
- 响应式设计
- 主题支持（深色/浅色）
- 动画和过渡效果

### HTML规范
- 语义化标签
- 无障碍访问支持
- 国际化考虑
- 性能优化

## 🚀 部署和发布

### 发布前检查
- [ ] 所有功能测试通过
- [ ] 图标文件完整
- [ ] 文档更新完整
- [ ] 版本号更新
- [ ] 安装脚本测试

### 打包发布
1. 清理开发文件
2. 压缩资源文件
3. 生成安装包
4. 编写发布说明
5. 上传到分发平台

## 📞 技术支持

### 开发团队联系方式
- 技术支持：<EMAIL>
- 开发者QQ群：123456789
- GitHub仓库：https://github.com/your-repo

### 贡献指南
1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 代码审查和合并

---

**注意**：本项目遵循MIT开源协议，欢迎贡献代码和反馈问题。
