@echo off
chcp 65001 >nul
echo ========================================
echo 图转层插件安装检查工具
echo ========================================
echo.

echo [*] 正在检查Photoshop安装...

:: 检查Photoshop进程
tasklist /FI "IMAGENAME eq Photoshop.exe" 2>NUL | find /I /N "Photoshop.exe" >nul
if %errorLevel% == 0 (
    echo [!] Photoshop正在运行，请先关闭Photoshop
    echo.
)

:: 检查可能的安装路径
echo [*] 检查可能的扩展目录：
echo.

set "found=0"

:: 检查Program Files中的Photoshop
for %%v in (2025 2024 2023 2022 2021) do (
    set "path=C:\Program Files\Adobe\Adobe Photoshop %%v\Plug-ins\Extensions"
    if exist "!path!" (
        echo [✓] 找到: !path!
        if exist "!path!\图转层" (
            echo     └─ [✓] 插件已安装
        ) else (
            echo     └─ [✗] 插件未安装
        )
        set "found=1"
    )
)

:: 检查CEP扩展目录
set "cep_path=%APPDATA%\Adobe\CEP\extensions"
if exist "%cep_path%" (
    echo [✓] 找到CEP目录: %cep_path%
    if exist "%cep_path%\图转层" (
        echo     └─ [✓] 插件已安装
    ) else (
        echo     └─ [✗] 插件未安装
    )
    set "found=1"
)

:: 检查用户文档目录
set "user_path=%USERPROFILE%\Documents\Adobe\CEP\extensions"
if exist "%user_path%" (
    echo [✓] 找到用户CEP目录: %user_path%
    if exist "%user_path%\图转层" (
        echo     └─ [✓] 插件已安装
    ) else (
        echo     └─ [✗] 插件未安装
    )
    set "found=1"
)

if %found%==0 (
    echo [✗] 未找到任何Photoshop扩展目录
    echo.
    echo 可能的原因：
    echo 1. Photoshop未正确安装
    echo 2. 版本不支持（需要2021或更高版本）
    echo 3. 扩展功能被禁用
    echo.
    goto :end
)

echo.
echo ========================================
echo 手动安装步骤：
echo ========================================
echo.
echo 1. 找到您的Photoshop安装目录，通常在：
echo    C:\Program Files\Adobe\Adobe Photoshop [版本]\Plug-ins\Extensions\
echo.
echo 2. 或者使用CEP扩展目录：
echo    %APPDATA%\Adobe\CEP\extensions\
echo.
echo 3. 在该目录下创建"图转层"文件夹
echo.
echo 4. 将以下文件复制到"图转层"文件夹：
echo    - manifest.json
echo    - index.html
echo    - package.json
echo    - css文件夹（包含所有内容）
echo    - js文件夹（包含所有内容）
echo    - icons文件夹（包含所有内容）
echo.
echo 5. 重启Photoshop
echo.
echo 6. 在菜单栏选择：窗口 > 扩展功能 > 图转层
echo.

echo ========================================
echo 启用扩展功能：
echo ========================================
echo.
echo 如果插件已安装但不显示，请检查：
echo.
echo 1. 在Photoshop中，选择：编辑 > 首选项 > 增效工具
echo 2. 勾选"载入扩展面板"
echo 3. 重启Photoshop
echo.
echo 或者：
echo 1. 选择：窗口 > 扩展功能
echo 2. 查看是否有"图转层"选项
echo.

:end
echo ========================================
echo 需要帮助？
echo ========================================
echo.
echo 如果仍然无法安装，请：
echo 1. 截图发送当前Photoshop版本信息
echo 2. 截图发送扩展功能菜单
echo 3. 提供详细的错误信息
echo.
pause
