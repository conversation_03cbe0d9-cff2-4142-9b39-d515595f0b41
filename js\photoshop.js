/**
 * Photoshop API 封装类
 * 处理与Photoshop的交互操作
 */
class PhotoshopAPI {
    constructor() {
        // 检测环境类型
        if (typeof window.csInterface !== 'undefined') {
            // CEP环境
            this.csInterface = window.csInterface;
            this.isCEP = true;
        } else if (typeof require !== 'undefined') {
            // UXP环境
            try {
                this.app = require('photoshop').app;
                this.core = require('photoshop').core;
                this.action = require('photoshop').action;
                this.isCEP = false;
            } catch (e) {
                console.warn('UXP模块加载失败，使用CEP模式');
                this.isCEP = true;
            }
        } else {
            this.isCEP = true;
        }

        console.log('Photoshop API初始化，CEP模式:', this.isCEP);
    }

    /**
     * 获取当前活动文档
     */
    async getActiveDocument() {
        try {
            return this.app.activeDocument;
        } catch (error) {
            throw new Error('没有打开的文档');
        }
    }

    /**
     * 获取当前选中的图层
     */
    async getSelectedLayers() {
        try {
            if (this.isCEP) {
                return new Promise((resolve, reject) => {
                    this.csInterface.getSelectedLayers((result) => {
                        try {
                            const layers = JSON.parse(result);
                            resolve(layers);
                        } catch (e) {
                            reject(new Error('解析图层数据失败'));
                        }
                    });
                });
            } else {
                const doc = await this.getActiveDocument();
                return doc.activeLayers;
            }
        } catch (error) {
            throw new Error('无法获取选中的图层');
        }
    }

    /**
     * 获取选区信息
     */
    async getSelection() {
        try {
            const doc = await this.getActiveDocument();
            return doc.selection;
        } catch (error) {
            return null;
        }
    }

    /**
     * 导出图层为临时文件
     */
    async exportLayerToFile(layer, filePath) {
        try {
            if (this.isCEP) {
                // CEP环境：使用ExtendScript
                return new Promise((resolve, reject) => {
                    this.csInterface.exportLayerToFile(layer.id, filePath, (result) => {
                        if (result && result !== 'null') {
                            resolve(result);
                        } else {
                            reject(new Error('导出图层失败'));
                        }
                    });
                });
            } else {
                // UXP环境
                const doc = await this.getActiveDocument();

                // 创建临时文档
                const tempDoc = await this.app.documents.add({
                    width: doc.width,
                    height: doc.height,
                    resolution: doc.resolution,
                    mode: 'RGBColorMode'
                });

                // 复制图层到临时文档
                await layer.duplicate(tempDoc);

                // 导出为PNG
                const exportOptions = {
                    format: 'png',
                    quality: 100,
                    transparency: true
                };

                await tempDoc.saveAs(filePath, exportOptions);
                await tempDoc.close();

                return filePath;
            }
        } catch (error) {
            throw new Error(`导出图层失败: ${error.message}`);
        }
    }

    /**
     * 获取图层的Canvas表示（用于OCR）
     */
    async getLayerAsCanvas(layer) {
        try {
            if (this.isCEP) {
                // CEP环境：通过ExtendScript获取图层数据
                return new Promise((resolve, reject) => {
                    this.csInterface.evalScript(`getLayerImageData("${layer.id}")`, (result) => {
                        try {
                            const imageData = JSON.parse(result);
                            const canvas = this.createCanvasFromImageData(imageData);
                            resolve(canvas);
                        } catch (e) {
                            reject(new Error('解析图层数据失败'));
                        }
                    });
                });
            } else {
                // UXP环境：直接处理
                const bounds = await this.getLayerBounds(layer);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                canvas.width = bounds.right - bounds.left;
                canvas.height = bounds.bottom - bounds.top;

                // 这里需要实际的图层像素数据
                // 暂时创建一个模拟的Canvas
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = 'black';
                ctx.font = '16px Arial';
                ctx.fillText('示例文字', 10, 30);

                return canvas;
            }
        } catch (error) {
            throw new Error(`获取图层Canvas失败: ${error.message}`);
        }
    }

    /**
     * 从图像数据创建Canvas
     */
    createCanvasFromImageData(imageData) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = imageData.width;
        canvas.height = imageData.height;

        if (imageData.pixels) {
            const imgData = ctx.createImageData(imageData.width, imageData.height);
            imgData.data.set(imageData.pixels);
            ctx.putImageData(imgData, 0, 0);
        }

        return canvas;
    }

    /**
     * 创建文本图层
     */
    async createTextLayer(text, options = {}) {
        try {
            const doc = await this.getActiveDocument();

            const textLayer = await doc.artLayers.add();
            textLayer.kind = 'text';
            textLayer.name = options.name || '识别文字';

            const textItem = textLayer.textItem;
            textItem.contents = text;

            // 设置文本属性
            if (options.fontSize) {
                textItem.size = options.fontSize;
            }
            if (options.color) {
                textItem.color = this.createSolidColor(options.color);
            }
            if (options.font) {
                textItem.font = options.font;
            }
            if (options.position) {
                textItem.position = [options.position.x, options.position.y];
            }

            return textLayer;
        } catch (error) {
            throw new Error(`创建文本图层失败: ${error.message}`);
        }
    }

    /**
     * 创建颜色对象
     */
    createSolidColor(rgb) {
        const color = new this.app.SolidColor();
        color.rgb.red = rgb.r || 0;
        color.rgb.green = rgb.g || 0;
        color.rgb.blue = rgb.b || 0;
        return color;
    }

    /**
     * 删除图层背景
     */
    async removeBackground(layer) {
        try {
            // 选择图层
            await this.selectLayer(layer);

            // 使用魔棒工具选择背景
            await this.action.batchPlay([
                {
                    "_obj": "selectColor",
                    "tolerance": 32,
                    "antiAlias": true,
                    "contiguous": true
                }
            ], {});

            // 删除选区
            await this.action.batchPlay([
                {
                    "_obj": "delete"
                }
            ], {});

            // 取消选区
            await this.action.batchPlay([
                {
                    "_obj": "set",
                    "_target": [{"_ref": "channel", "_property": "selection"}],
                    "to": {"_enum": "ordinal", "_value": "none"}
                }
            ], {});

        } catch (error) {
            throw new Error(`删除背景失败: ${error.message}`);
        }
    }

    /**
     * 选择图层
     */
    async selectLayer(layer) {
        try {
            await this.action.batchPlay([
                {
                    "_obj": "select",
                    "_target": [{"_ref": "layer", "_id": layer.id}]
                }
            ], {});
        } catch (error) {
            throw new Error(`选择图层失败: ${error.message}`);
        }
    }

    /**
     * 复制图层
     */
    async duplicateLayer(layer, name) {
        try {
            const duplicatedLayer = await layer.duplicate();
            if (name) {
                duplicatedLayer.name = name;
            }
            return duplicatedLayer;
        } catch (error) {
            throw new Error(`复制图层失败: ${error.message}`);
        }
    }

    /**
     * 创建图层蒙版
     */
    async createLayerMask(layer) {
        try {
            await this.selectLayer(layer);

            await this.action.batchPlay([
                {
                    "_obj": "make",
                    "_target": [{"_ref": "channel"}],
                    "at": {"_ref": "channel", "_enum": "channel", "_value": "mask"},
                    "using": {"_enum": "userMaskEnabled", "_value": "revealAll"}
                }
            ], {});

        } catch (error) {
            throw new Error(`创建图层蒙版失败: ${error.message}`);
        }
    }

    /**
     * 获取文档信息
     */
    async getDocumentInfo() {
        try {
            const doc = await this.getActiveDocument();
            return {
                width: doc.width.value,
                height: doc.height.value,
                resolution: doc.resolution,
                colorMode: doc.mode,
                name: doc.name
            };
        } catch (error) {
            throw new Error(`获取文档信息失败: ${error.message}`);
        }
    }

    /**
     * 显示进度条
     */
    async showProgress(message, progress = 0) {
        try {
            await this.core.showAlert(message);
        } catch (error) {
            console.log(message);
        }
    }

    /**
     * 显示警告对话框
     */
    async showAlert(message) {
        try {
            await this.core.showAlert(message);
        } catch (error) {
            console.error('Alert error:', error);
        }
    }

    /**
     * 获取图层边界
     */
    async getLayerBounds(layer) {
        try {
            return {
                left: layer.bounds.left.value,
                top: layer.bounds.top.value,
                right: layer.bounds.right.value,
                bottom: layer.bounds.bottom.value
            };
        } catch (error) {
            throw new Error(`获取图层边界失败: ${error.message}`);
        }
    }
}

// 导出实例
window.psAPI = new PhotoshopAPI();
