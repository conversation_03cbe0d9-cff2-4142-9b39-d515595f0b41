@echo off
echo 正在测试PS扩展安装...

REM 检查Photoshop是否正在运行
tasklist /FI "IMAGENAME eq Photoshop.exe" 2>NUL | find /I /N "Photoshop.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo 警告: Photoshop正在运行，请先关闭Photoshop再进行测试
    pause
    exit /b 1
)

REM 检查扩展目录是否存在
set "EXTENSION_DIR=%~dp0"
echo 扩展目录: %EXTENSION_DIR%

REM 检查必要文件
echo 检查必要文件...
if not exist "%EXTENSION_DIR%index.html" (
    echo 错误: index.html 文件不存在
    pause
    exit /b 1
)

if not exist "%EXTENSION_DIR%CSXS\manifest.xml" (
    echo 错误: manifest.xml 文件不存在
    pause
    exit /b 1
)

if not exist "%EXTENSION_DIR%js\main.js" (
    echo 错误: main.js 文件不存在
    pause
    exit /b 1
)

echo 所有必要文件检查完成

REM 检查CEP调试模式
echo 检查CEP调试模式...
reg query "HKEY_CURRENT_USER\Software\Adobe\CSXS.11" /v PlayerDebugMode 2>nul
if %errorlevel% neq 0 (
    echo 设置CEP调试模式...
    reg add "HKEY_CURRENT_USER\Software\Adobe\CSXS.11" /v PlayerDebugMode /t REG_SZ /d 1 /f
    echo CEP调试模式已启用
) else (
    echo CEP调试模式已存在
)

REM 启动Photoshop进行测试
echo 准备启动Photoshop进行测试...
echo 请在Photoshop中通过 窗口 > 扩展功能 > 图转层 来打开插件
echo.
echo 如果插件无法加载，请检查：
echo 1. Photoshop版本是否支持（需要2021或更高版本）
echo 2. CEP调试模式是否正确启用
echo 3. 扩展文件是否完整
echo.

REM 尝试启动Photoshop
set "PS_PATH=C:\Program Files\Adobe\Adobe Photoshop 2025\Photoshop.exe"
if exist "%PS_PATH%" (
    echo 启动Photoshop...
    start "" "%PS_PATH%"
) else (
    echo 未找到Photoshop 2025，请手动启动Photoshop
)

echo.
echo 测试完成！
echo 如果遇到问题，请查看debug.html页面进行调试
pause
