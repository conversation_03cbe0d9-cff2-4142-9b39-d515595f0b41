<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版本 - 图转层插件</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            font-size: 12px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
        }
        .debug-log {
            margin-bottom: 5px;
            padding: 2px;
        }
        .debug-error {
            color: red;
        }
        .debug-warn {
            color: orange;
        }
        .debug-info-text {
            color: blue;
        }
        .clear-debug {
            background: #007cba;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- 调试信息面板 -->
    <div class="debug-info">
        <button class="clear-debug" onclick="clearDebugLog()">清空日志</button>
        <div id="debugLog">调试日志将显示在这里...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>图转层 (调试版本)</h1>
            <p class="subtitle">PS图片转图层插件 - 调试模式</p>
        </div>

        <div class="main-content">
            <!-- OCR文字识别区域 -->
            <div class="section">
                <h2>📝 文字识别</h2>
                <div class="button-group">
                    <button id="extractTextOnly" class="btn btn-primary">提取文字</button>
                    <button id="extractTextWithLayer" class="btn btn-secondary">文字分层</button>
                </div>
                <p class="help-text">支持中文、英文、日文、韩文、俄文等多语言识别</p>
            </div>

            <!-- 背景分离区域 -->
            <div class="section">
                <h2>🎨 背景分离</h2>
                <div class="button-group">
                    <button id="removeBackground" class="btn btn-warning" title="删除背景">
                        <span class="icon">🗑️</span>
                        删除背景
                    </button>
                    <button id="separateBackground" class="btn btn-info" title="背景分离">
                        <span class="icon">✂️</span>
                        背景分离
                    </button>
                </div>
                <p class="help-text">将图片元素和背景分离，达到PSD分层效果</p>
            </div>

            <!-- 设置区域 -->
            <div class="section">
                <h2>⚙️ 设置</h2>
                <button id="openSettings" class="btn btn-settings">
                    <span class="icon">🔧</span>
                    OCR设置
                </button>
                <p class="help-text">配置第三方OCR密钥，专享识别额度</p>
            </div>

            <!-- 状态显示 -->
            <div class="status-section">
                <div id="statusBar" class="status-bar">
                    <span id="statusText">就绪</span>
                    <div id="progressBar" class="progress-bar" style="display: none;">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置面板 -->
        <div id="settingsPanel" class="settings-panel" style="display: none;">
            <div class="settings-content">
                <h3>OCR设置</h3>
                <div class="form-group">
                    <label for="ocrProvider">OCR服务商：</label>
                    <select id="ocrProvider">
                        <option value="free">免费服务</option>
                        <option value="baidu">百度OCR</option>
                        <option value="google">Google Vision</option>
                        <option value="ocrspace">OCR.space</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="apiKey">API密钥：</label>
                    <input type="password" id="apiKey" placeholder="请输入API密钥">
                </div>
                <div class="form-group">
                    <label for="language">识别语言：</label>
                    <select id="language">
                        <option value="auto">自动检测</option>
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="ru">俄文</option>
                    </select>
                </div>
                <div class="button-group">
                    <button id="saveSettings" class="btn btn-primary">保存</button>
                    <button id="cancelSettings" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 调试日志功能
        function addDebugLog(message, type = 'info') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `debug-log debug-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '调试日志将显示在这里...';
        }

        // 重写console方法
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addDebugLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addDebugLog(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addDebugLog(args.join(' '), 'warn');
        };

        addDebugLog('调试页面已加载');
    </script>

    <!-- CEP支持 -->
    <script src="js/CSInterface.js"></script>
    <script src="js/photoshop.js"></script>
    <script src="js/ocr.js"></script>
    <script src="js/background-removal.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
