/**
 * 主应用程序类
 * 协调各个模块的交互
 */
class ImageToTextApp {
    constructor() {
        this.psAPI = window.psAPI;
        this.ocrService = window.ocrService;
        this.backgroundRemovalService = window.backgroundRemovalService;
        this.isProcessing = false;
        this.clickCount = 0;
        this.lastClickTime = 0;
        
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.bindEvents();
        this.updateStatus('就绪');
        console.log('图转层插件已加载');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // OCR文字识别按钮
        document.getElementById('extractTextOnly').addEventListener('click', (e) => {
            this.handleTextExtraction(false);
        });

        document.getElementById('extractTextWithLayer').addEventListener('click', (e) => {
            this.handleTextExtraction(true);
        });

        // 背景分离按钮
        document.getElementById('removeBackground').addEventListener('click', (e) => {
            this.handleBackgroundRemoval();
        });

        document.getElementById('separateBackground').addEventListener('click', (e) => {
            this.handleBackgroundSeparation();
        });

        // 设置按钮
        document.getElementById('openSettings').addEventListener('click', (e) => {
            this.showSettings();
        });

        // 设置面板事件
        document.getElementById('saveSettings').addEventListener('click', (e) => {
            this.saveSettings();
        });

        document.getElementById('cancelSettings').addEventListener('click', (e) => {
            this.hideSettings();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 鼠标事件模拟
        this.setupMouseEvents();
    }

    /**
     * 设置鼠标事件模拟
     */
    setupMouseEvents() {
        // 模拟鼠标左键、右键、中键功能
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            // 右键：文字分层或背景分离
            if (e.target.closest('#extractTextOnly') || e.target.closest('#extractTextWithLayer')) {
                this.handleTextExtraction(true);
            } else if (e.target.closest('#removeBackground')) {
                this.handleBackgroundSeparation();
            }
        });

        document.addEventListener('auxclick', (e) => {
            if (e.button === 1) { // 中键
                e.preventDefault();
                this.showSettings();
            }
        });
    }

    /**
     * 处理文字提取
     */
    async handleTextExtraction(withLayer = false) {
        if (this.isProcessing) return;

        try {
            this.isProcessing = true;
            this.updateStatus('正在处理...', true);

            // 获取当前选中的图层或选区
            const selectedLayers = await this.psAPI.getSelectedLayers();
            const selection = await this.psAPI.getSelection();

            if (!selectedLayers || selectedLayers.length === 0) {
                throw new Error('请先选择要处理的图层');
            }

            for (const layer of selectedLayers) {
                await this.processLayerOCR(layer, selection, withLayer);
            }

            this.updateStatus('文字提取完成！');
            
        } catch (error) {
            this.updateStatus(`错误: ${error.message}`);
            await this.psAPI.showAlert(error.message);
        } finally {
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 处理单个图层的OCR
     */
    async processLayerOCR(layer, selection, withLayer) {
        try {
            // 导出图层为临时文件
            const tempPath = require('path').join(require('os').tmpdir(), `ocr_temp_${Date.now()}.png`);
            await this.psAPI.exportLayerToFile(layer, tempPath);

            // 进行OCR识别
            this.updateStatus('正在识别文字...');
            const ocrResult = await this.ocrService.recognizeText(tempPath);

            if (!ocrResult.success || !ocrResult.results.length) {
                throw new Error('未识别到文字内容');
            }

            // 获取图层信息用于坐标转换
            const layerBounds = await this.psAPI.getLayerBounds(layer);
            const docInfo = await this.psAPI.getDocumentInfo();

            // 创建文字图层
            this.updateStatus('正在创建文字图层...');
            for (const textResult of ocrResult.results) {
                await this.createTextLayerFromOCR(textResult, layerBounds, docInfo);
            }

            // 如果需要分层，处理背景分离
            if (withLayer) {
                this.updateStatus('正在处理图层分离...');
                await this.backgroundRemovalService.processLayerSeparation(layer, selection);
            }

            // 清理临时文件
            try {
                require('fs').unlinkSync(tempPath);
            } catch (e) {
                console.warn('清理临时文件失败:', e);
            }

        } catch (error) {
            throw new Error(`处理图层OCR失败: ${error.message}`);
        }
    }

    /**
     * 从OCR结果创建文字图层
     */
    async createTextLayerFromOCR(textResult, layerBounds, docInfo) {
        try {
            const options = {
                name: `识别文字_${textResult.text.substring(0, 10)}`,
                fontSize: textResult.fontSize,
                color: textResult.color,
                font: textResult.font,
                position: {
                    x: layerBounds.left + textResult.bbox.x,
                    y: layerBounds.top + textResult.bbox.y
                }
            };

            await this.psAPI.createTextLayer(textResult.text, options);
            
        } catch (error) {
            console.error('创建文字图层失败:', error);
        }
    }

    /**
     * 处理背景移除
     */
    async handleBackgroundRemoval() {
        if (this.isProcessing) return;

        try {
            this.isProcessing = true;
            
            // 检测双击
            const currentTime = Date.now();
            if (currentTime - this.lastClickTime < 500) {
                this.clickCount++;
            } else {
                this.clickCount = 1;
            }
            this.lastClickTime = currentTime;

            if (this.clickCount === 1) {
                // 第一次点击：删除背景
                setTimeout(async () => {
                    if (this.clickCount === 1) {
                        this.updateStatus('正在删除背景...', true);
                        await this.backgroundRemovalService.removeBackground();
                        this.updateStatus('背景删除完成！');
                        this.isProcessing = false;
                        this.hideProgress();
                    }
                }, 500);
            } else if (this.clickCount === 2) {
                // 第二次点击：创建图层蒙版
                this.updateStatus('正在创建图层蒙版...', true);
                await this.backgroundRemovalService.createLayerMask();
                this.updateStatus('图层蒙版创建完成！');
                this.clickCount = 0;
                this.isProcessing = false;
                this.hideProgress();
            }

        } catch (error) {
            this.updateStatus(`错误: ${error.message}`);
            await this.psAPI.showAlert(error.message);
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 处理背景分离
     */
    async handleBackgroundSeparation() {
        if (this.isProcessing) return;

        try {
            this.isProcessing = true;
            this.updateStatus('正在分离背景...', true);

            await this.backgroundRemovalService.separateBackground();
            this.updateStatus('背景分离完成！');

        } catch (error) {
            this.updateStatus(`错误: ${error.message}`);
            await this.psAPI.showAlert(error.message);
        } finally {
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 显示设置面板
     */
    showSettings() {
        const panel = document.getElementById('settingsPanel');
        const settings = this.ocrService.loadSettings();
        
        document.getElementById('ocrProvider').value = settings.provider;
        document.getElementById('apiKey').value = settings.apiKey;
        document.getElementById('language').value = settings.language;
        
        panel.style.display = 'flex';
    }

    /**
     * 隐藏设置面板
     */
    hideSettings() {
        document.getElementById('settingsPanel').style.display = 'none';
    }

    /**
     * 保存设置
     */
    saveSettings() {
        const settings = {
            provider: document.getElementById('ocrProvider').value,
            apiKey: document.getElementById('apiKey').value,
            language: document.getElementById('language').value
        };

        this.ocrService.saveSettings(settings);
        this.hideSettings();
        this.updateStatus('设置已保存');
    }

    /**
     * 处理键盘事件
     */
    handleKeyboard(e) {
        // Ctrl+Shift+T: 文字提取
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            this.handleTextExtraction(false);
        }
        
        // Ctrl+Shift+L: 文字分层
        if (e.ctrlKey && e.shiftKey && e.key === 'L') {
            e.preventDefault();
            this.handleTextExtraction(true);
        }
        
        // Ctrl+Shift+B: 背景移除
        if (e.ctrlKey && e.shiftKey && e.key === 'B') {
            e.preventDefault();
            this.handleBackgroundRemoval();
        }
        
        // Ctrl+Shift+S: 背景分离
        if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            this.handleBackgroundSeparation();
        }
    }

    /**
     * 更新状态
     */
    updateStatus(message, showProgress = false) {
        document.getElementById('statusText').textContent = message;
        
        if (showProgress) {
            this.showProgress();
        }
        
        console.log(`状态: ${message}`);
    }

    /**
     * 显示进度条
     */
    showProgress() {
        document.getElementById('progressBar').style.display = 'block';
    }

    /**
     * 隐藏进度条
     */
    hideProgress() {
        document.getElementById('progressBar').style.display = 'none';
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    const app = new ImageToTextApp();
    app.initialize();
});

// 导出应用实例
window.ImageToTextApp = ImageToTextApp;

