/**
 * 主应用程序类
 * 协调各个模块的交互
 */
class ImageToTextApp {
    constructor() {
        this.psAPI = window.psAPI;
        this.ocrService = window.ocrService;
        this.backgroundRemovalService = window.backgroundRemovalService;
        this.isProcessing = false;
        this.clickCount = 0;
        this.lastClickTime = 0;

        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.bindEvents();
        this.updateStatus('就绪');
        console.log('图转层插件已加载');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        console.log('开始绑定事件...');

        // 检查元素是否存在
        const elements = [
            'extractTextOnly',
            'extractTextWithLayer',
            'removeBackground',
            'separateBackground',
            'openSettings',
            'saveSettings',
            'cancelSettings'
        ];

        for (const elementId of elements) {
            const element = document.getElementById(elementId);
            if (!element) {
                console.error(`元素未找到: ${elementId}`);
            } else {
                console.log(`元素已找到: ${elementId}`);
            }
        }

        // OCR文字识别按钮
        const extractTextOnlyBtn = document.getElementById('extractTextOnly');
        if (extractTextOnlyBtn) {
            extractTextOnlyBtn.addEventListener('click', (e) => {
                console.log('提取文字按钮被点击');
                e.preventDefault();
                this.handleTextExtraction(false);
            });
            console.log('提取文字按钮事件已绑定');
        }

        const extractTextWithLayerBtn = document.getElementById('extractTextWithLayer');
        if (extractTextWithLayerBtn) {
            extractTextWithLayerBtn.addEventListener('click', (e) => {
                console.log('文字分层按钮被点击');
                e.preventDefault();
                this.handleTextExtraction(true);
            });
            console.log('文字分层按钮事件已绑定');
        }

        // 背景分离按钮
        const removeBackgroundBtn = document.getElementById('removeBackground');
        if (removeBackgroundBtn) {
            removeBackgroundBtn.addEventListener('click', (e) => {
                console.log('删除背景按钮被点击');
                e.preventDefault();
                this.handleBackgroundRemoval();
            });
            console.log('删除背景按钮事件已绑定');
        }

        const separateBackgroundBtn = document.getElementById('separateBackground');
        if (separateBackgroundBtn) {
            separateBackgroundBtn.addEventListener('click', (e) => {
                console.log('背景分离按钮被点击');
                e.preventDefault();
                this.handleBackgroundSeparation();
            });
            console.log('背景分离按钮事件已绑定');
        }

        // 设置按钮
        const openSettingsBtn = document.getElementById('openSettings');
        if (openSettingsBtn) {
            openSettingsBtn.addEventListener('click', (e) => {
                console.log('设置按钮被点击');
                e.preventDefault();
                this.showSettings();
            });
            console.log('设置按钮事件已绑定');
        }

        // 设置面板事件
        const saveSettingsBtn = document.getElementById('saveSettings');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', (e) => {
                console.log('保存设置按钮被点击');
                e.preventDefault();
                this.saveSettings();
            });
            console.log('保存设置按钮事件已绑定');
        }

        const cancelSettingsBtn = document.getElementById('cancelSettings');
        if (cancelSettingsBtn) {
            cancelSettingsBtn.addEventListener('click', (e) => {
                console.log('取消设置按钮被点击');
                e.preventDefault();
                this.hideSettings();
            });
            console.log('取消设置按钮事件已绑定');
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 鼠标事件模拟
        this.setupMouseEvents();

        console.log('所有事件绑定完成');
    }

    /**
     * 设置鼠标事件模拟
     */
    setupMouseEvents() {
        // 模拟鼠标左键、右键、中键功能
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            // 右键：文字分层或背景分离
            if (e.target.closest('#extractTextOnly') || e.target.closest('#extractTextWithLayer')) {
                this.handleTextExtraction(true);
            } else if (e.target.closest('#removeBackground')) {
                this.handleBackgroundSeparation();
            }
        });

        document.addEventListener('auxclick', (e) => {
            if (e.button === 1) { // 中键
                e.preventDefault();
                this.showSettings();
            }
        });
    }

    /**
     * 处理文字提取
     */
    async handleTextExtraction(withLayer = false) {
        console.log(`开始处理文字提取，withLayer: ${withLayer}`);

        if (this.isProcessing) {
            console.log('正在处理中，忽略此次点击');
            return;
        }

        try {
            this.isProcessing = true;
            this.updateStatus('正在处理...', true);

            // 检查是否在Photoshop环境中
            if (!this.psAPI || !this.psAPI.getSelectedLayers) {
                console.log('非Photoshop环境，使用模拟处理');
                await this.simulateTextExtraction(withLayer);
                return;
            }

            // 获取当前选中的图层或选区
            const selectedLayers = await this.psAPI.getSelectedLayers();
            const selection = await this.psAPI.getSelection();

            if (!selectedLayers || selectedLayers.length === 0) {
                throw new Error('请先选择要处理的图层');
            }

            for (const layer of selectedLayers) {
                await this.processLayerOCR(layer, selection, withLayer);
            }

            this.updateStatus('文字提取完成！');

        } catch (error) {
            console.error('文字提取失败:', error);
            this.updateStatus(`错误: ${error.message}`);
            if (this.psAPI && this.psAPI.showAlert) {
                await this.psAPI.showAlert(error.message);
            } else {
                alert(error.message);
            }
        } finally {
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 模拟文字提取（用于测试）
     */
    async simulateTextExtraction(withLayer) {
        console.log('模拟文字提取开始');

        // 模拟处理延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        this.updateStatus('模拟识别文字...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (withLayer) {
            this.updateStatus('模拟创建文字图层...');
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        this.updateStatus(withLayer ? '文字分层完成！' : '文字提取完成！');
        console.log('模拟文字提取完成');
    }

    /**
     * 处理单个图层的OCR
     */
    async processLayerOCR(layer, selection, withLayer) {
        try {
            console.log('开始处理图层OCR:', layer);

            // 获取图层的Canvas表示
            this.updateStatus('正在获取图层数据...');
            const canvas = await this.psAPI.getLayerAsCanvas(layer);

            if (!canvas) {
                throw new Error('无法获取图层数据');
            }

            // 进行OCR识别
            this.updateStatus('正在识别文字...');
            console.log('开始OCR识别，Canvas尺寸:', canvas.width, 'x', canvas.height);

            const ocrResult = await this.ocrService.recognizeText(canvas);
            console.log('OCR识别结果:', ocrResult);

            if (!ocrResult.success || !ocrResult.results.length) {
                throw new Error('未识别到文字内容');
            }

            // 获取图层信息用于坐标转换
            const layerBounds = await this.psAPI.getLayerBounds(layer);
            const docInfo = await this.psAPI.getDocumentInfo();

            // 创建文字图层
            this.updateStatus('正在创建文字图层...');
            console.log(`创建 ${ocrResult.results.length} 个文字图层`);

            for (const textResult of ocrResult.results) {
                await this.createTextLayerFromOCR(textResult, layerBounds, docInfo);
            }

            // 如果需要分层，处理背景分离
            if (withLayer) {
                this.updateStatus('正在处理图层分离...');
                await this.backgroundRemovalService.processLayerSeparation(layer, selection);
            }

            console.log('图层OCR处理完成');

        } catch (error) {
            console.error('处理图层OCR失败:', error);
            throw new Error(`处理图层OCR失败: ${error.message}`);
        }
    }

    /**
     * 获取临时文件路径
     */
    getTempFilePath(filename) {
        if (typeof require !== 'undefined') {
            try {
                const path = require('path');
                const os = require('os');
                return path.join(os.tmpdir(), filename);
            } catch (e) {
                console.warn('无法获取系统临时目录，使用默认路径');
            }
        }
        return `C:\\temp\\${filename}`;
    }

    /**
     * 清理临时文件
     */
    cleanupTempFile(filePath) {
        if (typeof require !== 'undefined') {
            try {
                const fs = require('fs');
                fs.unlinkSync(filePath);
            } catch (e) {
                console.warn('无法删除临时文件:', e);
            }
        } else {
            console.log('模拟删除临时文件:', filePath);
        }
    }

    /**
     * 从OCR结果创建文字图层
     */
    async createTextLayerFromOCR(textResult, layerBounds, docInfo) {
        try {
            const options = {
                name: `识别文字_${textResult.text.substring(0, 10)}`,
                fontSize: textResult.fontSize,
                color: textResult.color,
                font: textResult.font,
                position: {
                    x: layerBounds.left + textResult.bbox.x,
                    y: layerBounds.top + textResult.bbox.y
                }
            };

            await this.psAPI.createTextLayer(textResult.text, options);

        } catch (error) {
            console.error('创建文字图层失败:', error);
        }
    }

    /**
     * 处理背景移除
     */
    async handleBackgroundRemoval() {
        console.log('开始处理背景移除');

        if (this.isProcessing) {
            console.log('正在处理中，忽略此次点击');
            return;
        }

        try {
            this.isProcessing = true;

            // 检查是否在Photoshop环境中
            if (!this.backgroundRemovalService || !this.backgroundRemovalService.removeBackground) {
                console.log('非Photoshop环境，使用模拟处理');
                await this.simulateBackgroundRemoval();
                return;
            }

            // 检测双击
            const currentTime = Date.now();
            if (currentTime - this.lastClickTime < 500) {
                this.clickCount++;
            } else {
                this.clickCount = 1;
            }
            this.lastClickTime = currentTime;

            if (this.clickCount === 1) {
                // 第一次点击：删除背景
                setTimeout(async () => {
                    if (this.clickCount === 1) {
                        this.updateStatus('正在删除背景...', true);
                        await this.backgroundRemovalService.removeBackground();
                        this.updateStatus('背景删除完成！');
                        this.isProcessing = false;
                        this.hideProgress();
                    }
                }, 500);
            } else if (this.clickCount === 2) {
                // 第二次点击：创建图层蒙版
                this.updateStatus('正在创建图层蒙版...', true);
                await this.backgroundRemovalService.createLayerMask();
                this.updateStatus('图层蒙版创建完成！');
                this.clickCount = 0;
                this.isProcessing = false;
                this.hideProgress();
            }

        } catch (error) {
            console.error('背景移除失败:', error);
            this.updateStatus(`错误: ${error.message}`);
            if (this.psAPI && this.psAPI.showAlert) {
                await this.psAPI.showAlert(error.message);
            } else {
                alert(error.message);
            }
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 模拟背景移除（用于测试）
     */
    async simulateBackgroundRemoval() {
        console.log('模拟背景移除开始');

        this.updateStatus('正在删除背景...', true);
        await new Promise(resolve => setTimeout(resolve, 1500));

        this.updateStatus('背景删除完成！');
        this.isProcessing = false;
        this.hideProgress();
        console.log('模拟背景移除完成');
    }

    /**
     * 处理背景分离
     */
    async handleBackgroundSeparation() {
        console.log('开始处理背景分离');

        if (this.isProcessing) {
            console.log('正在处理中，忽略此次点击');
            return;
        }

        try {
            this.isProcessing = true;
            this.updateStatus('正在分离背景...', true);

            // 检查是否在Photoshop环境中
            if (!this.backgroundRemovalService || !this.backgroundRemovalService.separateBackground) {
                console.log('非Photoshop环境，使用模拟处理');
                await this.simulateBackgroundSeparation();
                return;
            }

            await this.backgroundRemovalService.separateBackground();
            this.updateStatus('背景分离完成！');

        } catch (error) {
            console.error('背景分离失败:', error);
            this.updateStatus(`错误: ${error.message}`);
            if (this.psAPI && this.psAPI.showAlert) {
                await this.psAPI.showAlert(error.message);
            } else {
                alert(error.message);
            }
        } finally {
            this.isProcessing = false;
            this.hideProgress();
        }
    }

    /**
     * 模拟背景分离（用于测试）
     */
    async simulateBackgroundSeparation() {
        console.log('模拟背景分离开始');

        await new Promise(resolve => setTimeout(resolve, 2000));
        this.updateStatus('背景分离完成！');
        console.log('模拟背景分离完成');
    }

    /**
     * 显示设置面板
     */
    showSettings() {
        console.log('显示设置面板');

        const panel = document.getElementById('settingsPanel');
        if (!panel) {
            console.error('设置面板元素未找到');
            alert('设置面板未找到');
            return;
        }

        try {
            const settings = this.ocrService ? this.ocrService.loadSettings() : {
                provider: 'free',
                apiKey: '',
                language: 'auto'
            };

            const providerSelect = document.getElementById('ocrProvider');
            const apiKeyInput = document.getElementById('apiKey');
            const languageSelect = document.getElementById('language');

            if (providerSelect) providerSelect.value = settings.provider;
            if (apiKeyInput) apiKeyInput.value = settings.apiKey;
            if (languageSelect) languageSelect.value = settings.language;

            panel.style.display = 'flex';
            console.log('设置面板已显示');
        } catch (error) {
            console.error('显示设置面板失败:', error);
            alert('显示设置面板失败: ' + error.message);
        }
    }

    /**
     * 隐藏设置面板
     */
    hideSettings() {
        console.log('隐藏设置面板');

        const panel = document.getElementById('settingsPanel');
        if (panel) {
            panel.style.display = 'none';
            console.log('设置面板已隐藏');
        } else {
            console.error('设置面板元素未找到');
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        console.log('保存设置');

        try {
            const settings = {
                provider: document.getElementById('ocrProvider')?.value || 'free',
                apiKey: document.getElementById('apiKey')?.value || '',
                language: document.getElementById('language')?.value || 'auto'
            };

            console.log('保存的设置:', settings);

            if (this.ocrService && this.ocrService.saveSettings) {
                this.ocrService.saveSettings(settings);
            } else {
                console.log('OCR服务不可用，设置保存到localStorage');
                localStorage.setItem('ocrSettings', JSON.stringify(settings));
            }

            this.hideSettings();
            this.updateStatus('设置已保存');
            console.log('设置保存完成');
        } catch (error) {
            console.error('保存设置失败:', error);
            alert('保存设置失败: ' + error.message);
        }
    }

    /**
     * 处理键盘事件
     */
    handleKeyboard(e) {
        // Ctrl+Shift+T: 文字提取
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            this.handleTextExtraction(false);
        }

        // Ctrl+Shift+L: 文字分层
        if (e.ctrlKey && e.shiftKey && e.key === 'L') {
            e.preventDefault();
            this.handleTextExtraction(true);
        }

        // Ctrl+Shift+B: 背景移除
        if (e.ctrlKey && e.shiftKey && e.key === 'B') {
            e.preventDefault();
            this.handleBackgroundRemoval();
        }

        // Ctrl+Shift+S: 背景分离
        if (e.ctrlKey && e.shiftKey && e.key === 'S') {
            e.preventDefault();
            this.handleBackgroundSeparation();
        }
    }

    /**
     * 更新状态
     */
    updateStatus(message, showProgress = false) {
        document.getElementById('statusText').textContent = message;

        if (showProgress) {
            this.showProgress();
        }

        console.log(`状态: ${message}`);
    }

    /**
     * 显示进度条
     */
    showProgress() {
        document.getElementById('progressBar').style.display = 'block';
    }

    /**
     * 隐藏进度条
     */
    hideProgress() {
        document.getElementById('progressBar').style.display = 'none';
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('开始初始化应用...');
        const app = new ImageToTextApp();
        window.app = app; // 导出到全局作用域便于调试
        console.log('应用初始化完成');
    } catch (error) {
        console.error('应用初始化失败:', error);
        alert('插件初始化失败: ' + error.message);
    }
});

// 导出应用实例
window.ImageToTextApp = ImageToTextApp;

