/**
 * 背景移除和图层分离类
 * 处理背景分离和图层处理功能
 */
class BackgroundRemovalService {
    constructor() {
        this.psAPI = window.psAPI;
        this.tempDir = require('os').tmpdir();
        this.fs = require('fs');
        this.path = require('path');
    }

    /**
     * 删除背景（第一次点击）
     */
    async removeBackground() {
        try {
            const selectedLayers = await this.psAPI.getSelectedLayers();
            
            if (!selectedLayers || selectedLayers.length === 0) {
                throw new Error('请先选择要处理的图层');
            }

            for (const layer of selectedLayers) {
                await this.processLayerBackgroundRemoval(layer);
            }

            await this.psAPI.showAlert('背景删除完成！');
            return true;
        } catch (error) {
            throw new Error(`删除背景失败: ${error.message}`);
        }
    }

    /**
     * 创建图层蒙版（第二次点击）
     */
    async createLayerMask() {
        try {
            const selectedLayers = await this.psAPI.getSelectedLayers();
            
            if (!selectedLayers || selectedLayers.length === 0) {
                throw new Error('请先选择要处理的图层');
            }

            for (const layer of selectedLayers) {
                await this.psAPI.createLayerMask(layer);
            }

            await this.psAPI.showAlert('图层蒙版创建完成！');
            return true;
        } catch (error) {
            throw new Error(`创建图层蒙版失败: ${error.message}`);
        }
    }

    /**
     * 背景分离（右键功能）
     */
    async separateBackground() {
        try {
            const selectedLayers = await this.psAPI.getSelectedLayers();
            const selection = await this.psAPI.getSelection();
            
            if (!selectedLayers || selectedLayers.length === 0) {
                throw new Error('请先选择要处理的图层');
            }

            for (const layer of selectedLayers) {
                await this.processLayerSeparation(layer, selection);
            }

            await this.psAPI.showAlert('背景分离完成！');
            return true;
        } catch (error) {
            throw new Error(`背景分离失败: ${error.message}`);
        }
    }

    /**
     * 处理单个图层的背景移除
     */
    async processLayerBackgroundRemoval(layer) {
        try {
            // 选择图层
            await this.psAPI.selectLayer(layer);
            
            // 使用魔棒工具选择背景
            await this.selectBackground(layer);
            
            // 删除选中的背景
            await this.deleteSelection();
            
            // 取消选区
            await this.clearSelection();
            
        } catch (error) {
            console.error(`处理图层背景移除失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 处理图层分离
     */
    async processLayerSeparation(layer, selection) {
        try {
            // 复制原图层作为背景层
            const backgroundLayer = await this.psAPI.duplicateLayer(layer, `${layer.name}_背景`);
            
            // 复制原图层作为前景层
            const foregroundLayer = await this.psAPI.duplicateLayer(layer, `${layer.name}_前景`);
            
            // 处理前景层 - 移除背景
            await this.psAPI.selectLayer(foregroundLayer);
            if (selection && selection.bounds) {
                // 如果有选区，反选后删除
                await this.invertSelection();
                await this.deleteSelection();
            } else {
                // 自动检测并移除背景
                await this.autoRemoveBackground(foregroundLayer);
            }
            
            // 处理背景层 - 移除前景
            await this.psAPI.selectLayer(backgroundLayer);
            if (selection && selection.bounds) {
                // 如果有选区，删除选区内容
                await this.deleteSelection();
            } else {
                // 自动检测并移除前景
                await this.autoRemoveForeground(backgroundLayer);
            }
            
            // 隐藏原图层
            layer.visible = false;
            
        } catch (error) {
            console.error(`处理图层分离失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 选择背景区域
     */
    async selectBackground(layer) {
        try {
            // 使用魔棒工具选择相似颜色区域
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "selectColor",
                    "tolerance": 32,
                    "antiAlias": true,
                    "contiguous": false,
                    "sampleAllLayers": false
                }
            ], {});
            
            // 扩展选区以包含边缘
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "expand",
                    "by": {"_unit": "pixelsUnit", "_value": 2}
                }
            ], {});
            
        } catch (error) {
            console.error(`选择背景失败: ${error.message}`);
        }
    }

    /**
     * 自动移除背景
     */
    async autoRemoveBackground(layer) {
        try {
            // 使用边缘检测和颜色分析来自动移除背景
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "selectColor",
                    "tolerance": 50,
                    "antiAlias": true,
                    "contiguous": false
                }
            ], {});
            
            await this.deleteSelection();
            
        } catch (error) {
            console.error(`自动移除背景失败: ${error.message}`);
        }
    }

    /**
     * 自动移除前景
     */
    async autoRemoveForeground(layer) {
        try {
            // 选择主要对象区域
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "selectColor",
                    "tolerance": 30,
                    "antiAlias": true,
                    "contiguous": true
                }
            ], {});
            
            // 反选
            await this.invertSelection();
            
            // 删除选区（保留背景）
            await this.deleteSelection();
            
        } catch (error) {
            console.error(`自动移除前景失败: ${error.message}`);
        }
    }

    /**
     * 删除选区内容
     */
    async deleteSelection() {
        try {
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "delete"
                }
            ], {});
        } catch (error) {
            console.error(`删除选区失败: ${error.message}`);
        }
    }

    /**
     * 反选
     */
    async invertSelection() {
        try {
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "inverse"
                }
            ], {});
        } catch (error) {
            console.error(`反选失败: ${error.message}`);
        }
    }

    /**
     * 清除选区
     */
    async clearSelection() {
        try {
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "set",
                    "_target": [{"_ref": "channel", "_property": "selection"}],
                    "to": {"_enum": "ordinal", "_value": "none"}
                }
            ], {});
        } catch (error) {
            console.error(`清除选区失败: ${error.message}`);
        }
    }

    /**
     * 使用AI背景移除服务
     */
    async aiBackgroundRemoval(imagePath) {
        try {
            // 这里可以集成第三方AI背景移除服务
            // 如 remove.bg API 或其他服务
            
            const formData = new FormData();
            const imageBlob = new Blob([this.fs.readFileSync(imagePath)]);
            formData.append('image_file', imageBlob);
            
            // 示例：使用remove.bg API
            // const response = await fetch('https://api.remove.bg/v1.0/removebg', {
            //     method: 'POST',
            //     headers: {
            //         'X-Api-Key': 'YOUR_API_KEY'
            //     },
            //     body: formData
            // });
            
            // 这里返回模拟结果
            return {
                success: true,
                processedImagePath: imagePath
            };
            
        } catch (error) {
            throw new Error(`AI背景移除失败: ${error.message}`);
        }
    }

    /**
     * 智能边缘检测
     */
    async smartEdgeDetection(layer) {
        try {
            await this.psAPI.selectLayer(layer);
            
            // 应用边缘检测滤镜
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "findEdges"
                }
            ], {});
            
            // 调整阈值
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "threshold",
                    "level": 128
                }
            ], {});
            
        } catch (error) {
            console.error(`智能边缘检测失败: ${error.message}`);
        }
    }

    /**
     * 羽化选区
     */
    async featherSelection(radius = 2) {
        try {
            await this.psAPI.action.batchPlay([
                {
                    "_obj": "feather",
                    "radius": {"_unit": "pixelsUnit", "_value": radius}
                }
            ], {});
        } catch (error) {
            console.error(`羽化选区失败: ${error.message}`);
        }
    }
}

// 导出实例
window.backgroundRemovalService = new BackgroundRemovalService();
