<?xml version="1.0" encoding="UTF-8"?>
<ExtensionManifest Version="7.0" ExtensionBundleId="com.ps.imagetotext" ExtensionBundleVersion="1.0.0"
                  ExtensionBundleName="图转层" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ExtensionList>
    <Extension Id="com.ps.imagetotext.panel" Version="1.0.0"/>
  </ExtensionList>
  <ExecutionEnvironment>
    <HostList>
      <Host Name="PHXS" Version="[22.0,99.9]"/>
      <Host Name="PHSP" Version="[22.0,99.9]"/>
    </HostList>
    <LocaleList>
      <Locale Code="All"/>
    </LocaleList>
    <RequiredRuntimeList>
      <RequiredRuntime Name="CSXS" Version="9.0"/>
    </RequiredRuntimeList>
  </ExecutionEnvironment>
  <DispatchInfoList>
    <Extension Id="com.ps.imagetotext.panel">
      <DispatchInfo>
        <Resources>
          <MainPath>./index.html</MainPath>
          <ScriptPath>./js/hostscript.jsx</ScriptPath>
        </Resources>
        <Lifecycle>
          <AutoVisible>true</AutoVisible>
        </Lifecycle>
        <UI>
          <Type>Panel</Type>
          <Menu>图转层</Menu>
          <Geometry>
            <Size>
              <Width>320</Width>
              <Height>600</Height>
            </Size>
            <MinSize>
              <Width>300</Width>
              <Height>400</Height>
            </MinSize>
            <MaxSize>
              <Width>400</Width>
              <Height>800</Height>
            </MaxSize>
          </Geometry>
          <Icons>
            <Icon Type="Normal">./icons/icon-light.png</Icon>
            <Icon Type="RollOver">./icons/icon-light.png</Icon>
            <Icon Type="DarkNormal">./icons/icon-dark.png</Icon>
            <Icon Type="DarkRollOver">./icons/icon-dark.png</Icon>
          </Icons>
        </UI>
      </DispatchInfo>
    </Extension>
  </DispatchInfoList>
</ExtensionManifest>
