<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR功能测试</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007cba;
        }
        .upload-area.dragover {
            border-color: #007cba;
            background-color: #f0f8ff;
        }
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .ocr-results {
            margin-top: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .text-result {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-left: 4px solid #007cba;
        }
        .progress-info {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        .provider-select {
            margin: 10px 0;
        }
        .provider-select select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>OCR文字识别测试</h1>
        
        <div class="provider-select">
            <label for="ocrProvider">选择OCR服务：</label>
            <select id="ocrProvider">
                <option value="tesseract">Tesseract.js (本地)</option>
                <option value="ocrspace">OCR.space (在线)</option>
                <option value="free">自动选择</option>
            </select>
        </div>

        <div class="upload-area" id="uploadArea">
            <p>点击或拖拽图片到这里进行OCR识别</p>
            <p style="font-size: 14px; color: #666;">支持 JPG, PNG, GIF 格式</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>

        <div id="imagePreview" style="display: none;">
            <h3>预览图片：</h3>
            <img id="previewImg" class="preview-image" alt="预览">
        </div>

        <div id="progressInfo" class="progress-info" style="display: none;"></div>

        <div class="button-group">
            <button id="startOCR" class="btn btn-primary" disabled>开始识别</button>
            <button id="clearResults" class="btn btn-secondary">清空结果</button>
        </div>

        <div id="ocrResults" class="ocr-results" style="display: none;">
            <h3>识别结果：</h3>
            <div id="resultsContainer"></div>
        </div>
    </div>

    <!-- 加载Tesseract.js -->
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    
    <!-- 加载我们的OCR服务 -->
    <script src="js/ocr.js"></script>

    <script>
        let currentImage = null;
        let ocrService = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('OCR测试页面加载完成');
            
            // 等待OCR服务加载
            if (window.ocrService) {
                ocrService = window.ocrService;
                console.log('OCR服务已加载');
            } else {
                console.error('OCR服务未加载');
            }

            setupEventListeners();
        });

        function setupEventListeners() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const startOCRBtn = document.getElementById('startOCR');
            const clearBtn = document.getElementById('clearResults');

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽功能
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            // 开始OCR
            startOCRBtn.addEventListener('click', startOCR);

            // 清空结果
            clearBtn.addEventListener('click', clearResults);
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                currentImage = e.target.result;
                showImagePreview(currentImage);
                document.getElementById('startOCR').disabled = false;
            };
            reader.readAsDataURL(file);
        }

        function showImagePreview(imageSrc) {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');
            
            img.src = imageSrc;
            preview.style.display = 'block';
        }

        async function startOCR() {
            if (!currentImage || !ocrService) {
                alert('请先选择图片并确保OCR服务已加载');
                return;
            }

            const provider = document.getElementById('ocrProvider').value;
            const progressInfo = document.getElementById('progressInfo');
            const startBtn = document.getElementById('startOCR');

            try {
                startBtn.disabled = true;
                progressInfo.style.display = 'block';
                progressInfo.textContent = '正在初始化OCR引擎...';

                // 创建图片元素
                const img = new Image();
                img.src = currentImage;
                
                await new Promise(resolve => img.onload = resolve);

                progressInfo.textContent = '正在识别文字...';

                // 根据选择的提供商进行OCR
                let result;
                if (provider === 'tesseract') {
                    result = await ocrService.tesseractOCR(img);
                } else if (provider === 'ocrspace') {
                    result = await ocrService.ocrSpaceOCR(currentImage);
                } else {
                    result = await ocrService.freeOCR(img);
                }

                progressInfo.textContent = '识别完成！';
                displayResults(result);

            } catch (error) {
                console.error('OCR识别失败:', error);
                progressInfo.textContent = '识别失败: ' + error.message;
                alert('OCR识别失败: ' + error.message);
            } finally {
                startBtn.disabled = false;
                setTimeout(() => {
                    progressInfo.style.display = 'none';
                }, 3000);
            }
        }

        function displayResults(result) {
            const resultsDiv = document.getElementById('ocrResults');
            const container = document.getElementById('resultsContainer');

            if (!result.success || !result.results.length) {
                container.innerHTML = '<p>未识别到文字内容</p>';
                resultsDiv.style.display = 'block';
                return;
            }

            let html = `<p><strong>识别提供商:</strong> ${result.provider}</p>`;
            html += `<p><strong>识别到 ${result.results.length} 个文字区域:</strong></p>`;

            result.results.forEach((item, index) => {
                html += `
                    <div class="text-result">
                        <strong>文字 ${index + 1}:</strong> ${item.text}<br>
                        <small>置信度: ${(item.confidence * 100).toFixed(1)}% | 
                        位置: (${item.bbox.x}, ${item.bbox.y}) | 
                        大小: ${item.bbox.width}×${item.bbox.height}</small>
                    </div>
                `;
            });

            container.innerHTML = html;
            resultsDiv.style.display = 'block';
        }

        function clearResults() {
            document.getElementById('ocrResults').style.display = 'none';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('progressInfo').style.display = 'none';
            document.getElementById('startOCR').disabled = true;
            document.getElementById('fileInput').value = '';
            currentImage = null;
        }
    </script>
</body>
</html>
