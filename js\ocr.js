/**
 * OCR 文字识别类
 * 支持多种OCR服务提供商
 */
class OCRService {
    constructor() {
        this.settings = this.loadSettings();
        // 在浏览器环境中模拟Node.js模块
        this.tempDir = this.getTempDir();
        this.fs = this.getFileSystem();
        this.path = this.getPathModule();
    }

    /**
     * 获取临时目录
     */
    getTempDir() {
        if (typeof require !== 'undefined') {
            try {
                return require('os').tmpdir();
            } catch (e) {
                console.warn('无法获取系统临时目录，使用默认路径');
            }
        }
        return 'C:\\temp'; // 默认临时目录
    }

    /**
     * 获取文件系统模块
     */
    getFileSystem() {
        if (typeof require !== 'undefined') {
            try {
                return require('fs');
            } catch (e) {
                console.warn('无法加载fs模块，使用模拟实现');
            }
        }
        // 模拟fs模块
        return {
            readFileSync: (path) => {
                console.log('模拟读取文件:', path);
                return new ArrayBuffer(0);
            },
            writeFileSync: (path, data) => {
                console.log('模拟写入文件:', path);
            },
            unlinkSync: (path) => {
                console.log('模拟删除文件:', path);
            }
        };
    }

    /**
     * 获取路径模块
     */
    getPathModule() {
        if (typeof require !== 'undefined') {
            try {
                return require('path');
            } catch (e) {
                console.warn('无法加载path模块，使用模拟实现');
            }
        }
        // 模拟path模块
        return {
            join: (...paths) => paths.join('\\'),
            dirname: (path) => path.substring(0, path.lastIndexOf('\\')),
            basename: (path) => path.substring(path.lastIndexOf('\\') + 1)
        };
    }

    /**
     * 加载设置
     */
    loadSettings() {
        try {
            const settings = localStorage.getItem('ocrSettings');
            return settings ? JSON.parse(settings) : {
                provider: 'free',
                apiKey: '',
                language: 'auto'
            };
        } catch (error) {
            return {
                provider: 'free',
                apiKey: '',
                language: 'auto'
            };
        }
    }

    /**
     * 保存设置
     */
    saveSettings(settings) {
        this.settings = { ...this.settings, ...settings };
        localStorage.setItem('ocrSettings', JSON.stringify(this.settings));
    }

    /**
     * 识别图片中的文字
     */
    async recognizeText(imagePath, options = {}) {
        try {
            const provider = options.provider || this.settings.provider;

            switch (provider) {
                case 'baidu':
                    return await this.baiduOCR(imagePath, options);
                case 'google':
                    return await this.googleOCR(imagePath, options);
                case 'ocrspace':
                    return await this.ocrSpaceOCR(imagePath, options);
                default:
                    return await this.freeOCR(imagePath, options);
            }
        } catch (error) {
            throw new Error(`OCR识别失败: ${error.message}`);
        }
    }

    /**
     * 免费OCR服务（使用Tesseract.js）
     */
    async freeOCR(imagePath, options = {}) {
        try {
            // 这里使用一个简化的OCR实现
            // 实际项目中需要集成Tesseract.js或其他免费OCR库

            const mockResults = [
                {
                    text: "示例文字",
                    confidence: 0.95,
                    bbox: { x: 100, y: 50, width: 120, height: 30 },
                    fontSize: 16,
                    color: { r: 0, g: 0, b: 0 },
                    font: "Arial"
                },
                {
                    text: "Sample Text",
                    confidence: 0.92,
                    bbox: { x: 100, y: 100, width: 150, height: 25 },
                    fontSize: 14,
                    color: { r: 0, g: 0, b: 0 },
                    font: "Arial"
                }
            ];

            return {
                success: true,
                results: mockResults,
                provider: 'free'
            };
        } catch (error) {
            throw new Error(`免费OCR识别失败: ${error.message}`);
        }
    }

    /**
     * 百度OCR
     */
    async baiduOCR(imagePath, options = {}) {
        try {
            const apiKey = this.settings.apiKey;
            if (!apiKey) {
                throw new Error('请先配置百度OCR API密钥');
            }

            // 读取图片文件
            const imageBuffer = this.fs.readFileSync(imagePath);
            const base64Image = imageBuffer.toString('base64');

            const response = await fetch('https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `access_token=${apiKey}&image=${encodeURIComponent(base64Image)}`
            });

            const result = await response.json();

            if (result.error_code) {
                throw new Error(`百度OCR错误: ${result.error_msg}`);
            }

            const results = result.words_result.map(item => ({
                text: item.words,
                confidence: item.probability || 0.9,
                bbox: this.parseBaiduLocation(item.location),
                fontSize: this.estimateFontSize(item.location),
                color: { r: 0, g: 0, b: 0 }, // 百度OCR不提供颜色信息
                font: "Arial"
            }));

            return {
                success: true,
                results: results,
                provider: 'baidu'
            };
        } catch (error) {
            throw new Error(`百度OCR识别失败: ${error.message}`);
        }
    }

    /**
     * Google Vision OCR
     */
    async googleOCR(imagePath, options = {}) {
        try {
            const apiKey = this.settings.apiKey;
            if (!apiKey) {
                throw new Error('请先配置Google Vision API密钥');
            }

            const imageBuffer = this.fs.readFileSync(imagePath);
            const base64Image = imageBuffer.toString('base64');

            const requestBody = {
                requests: [{
                    image: {
                        content: base64Image
                    },
                    features: [{
                        type: 'TEXT_DETECTION',
                        maxResults: 50
                    }]
                }]
            };

            const response = await fetch(`https://vision.googleapis.com/v1/images:annotate?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            const result = await response.json();

            if (result.responses[0].error) {
                throw new Error(`Google Vision错误: ${result.responses[0].error.message}`);
            }

            const textAnnotations = result.responses[0].textAnnotations || [];
            const results = textAnnotations.slice(1).map(annotation => ({
                text: annotation.description,
                confidence: 0.9,
                bbox: this.parseGoogleBoundingPoly(annotation.boundingPoly),
                fontSize: this.estimateFontSize(annotation.boundingPoly),
                color: { r: 0, g: 0, b: 0 },
                font: "Arial"
            }));

            return {
                success: true,
                results: results,
                provider: 'google'
            };
        } catch (error) {
            throw new Error(`Google Vision识别失败: ${error.message}`);
        }
    }

    /**
     * OCR.space OCR
     */
    async ocrSpaceOCR(imagePath, options = {}) {
        try {
            const apiKey = this.settings.apiKey || 'helloworld'; // 免费API密钥

            const imageBuffer = this.fs.readFileSync(imagePath);
            const base64Image = imageBuffer.toString('base64');

            const formData = new FormData();
            formData.append('base64Image', `data:image/png;base64,${base64Image}`);
            formData.append('language', this.settings.language === 'auto' ? 'eng' : this.settings.language);
            formData.append('isOverlayRequired', 'true');

            const response = await fetch('https://api.ocr.space/parse/image', {
                method: 'POST',
                headers: {
                    'apikey': apiKey
                },
                body: formData
            });

            const result = await response.json();

            if (!result.IsErroredOnProcessing) {
                const results = result.ParsedResults[0].TextOverlay.Lines.map(line =>
                    line.Words.map(word => ({
                        text: word.WordText,
                        confidence: 0.9,
                        bbox: {
                            x: word.Left,
                            y: word.Top,
                            width: word.Width,
                            height: word.Height
                        },
                        fontSize: word.Height * 0.8,
                        color: { r: 0, g: 0, b: 0 },
                        font: "Arial"
                    }))
                ).flat();

                return {
                    success: true,
                    results: results,
                    provider: 'ocrspace'
                };
            } else {
                throw new Error(result.ErrorMessage);
            }
        } catch (error) {
            throw new Error(`OCR.space识别失败: ${error.message}`);
        }
    }

    /**
     * 解析百度OCR位置信息
     */
    parseBaiduLocation(location) {
        return {
            x: location.left,
            y: location.top,
            width: location.width,
            height: location.height
        };
    }

    /**
     * 解析Google Vision边界多边形
     */
    parseGoogleBoundingPoly(boundingPoly) {
        const vertices = boundingPoly.vertices;
        const minX = Math.min(...vertices.map(v => v.x || 0));
        const minY = Math.min(...vertices.map(v => v.y || 0));
        const maxX = Math.max(...vertices.map(v => v.x || 0));
        const maxY = Math.max(...vertices.map(v => v.y || 0));

        return {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY
        };
    }

    /**
     * 估算字体大小
     */
    estimateFontSize(bbox) {
        const height = bbox.height || bbox.Height || 20;
        return Math.max(12, Math.round(height * 0.8));
    }

    /**
     * 分析文字颜色（简化实现）
     */
    async analyzeTextColor(imagePath, bbox) {
        // 这里应该实现图片颜色分析
        // 简化返回黑色
        return { r: 0, g: 0, b: 0 };
    }

    /**
     * 检测文字角度
     */
    detectTextAngle(bbox) {
        // 简化实现，返回0度
        return 0;
    }
}

// 导出实例
window.ocrService = new OCRService();
