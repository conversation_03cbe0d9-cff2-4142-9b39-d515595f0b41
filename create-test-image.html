<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试图片</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .canvas-container {
            border: 1px solid #ddd;
            margin: 20px 0;
            text-align: center;
        }
        .controls {
            margin: 20px 0;
        }
        .controls input, .controls select, .controls button {
            margin: 5px;
            padding: 8px;
        }
        .btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>创建OCR测试图片</h1>
    
    <div class="controls">
        <label>文字内容：</label>
        <input type="text" id="textInput" value="Hello World 你好世界" style="width: 200px;">
        
        <label>字体大小：</label>
        <select id="fontSize">
            <option value="16">16px</option>
            <option value="20" selected>20px</option>
            <option value="24">24px</option>
            <option value="32">32px</option>
            <option value="48">48px</option>
        </select>
        
        <label>字体：</label>
        <select id="fontFamily">
            <option value="Arial">Arial</option>
            <option value="SimSun">宋体</option>
            <option value="Microsoft YaHei">微软雅黑</option>
            <option value="Times New Roman">Times New Roman</option>
        </select>
        
        <button class="btn" onclick="generateImage()">生成图片</button>
        <button class="btn" onclick="downloadImage()">下载图片</button>
        <button class="btn" onclick="testOCR()">测试OCR</button>
    </div>

    <div class="canvas-container">
        <canvas id="testCanvas" width="400" height="200" style="border: 1px solid #ccc;"></canvas>
    </div>

    <div id="ocrResult" style="margin-top: 20px; padding: 15px; background: #f9f9f9; display: none;">
        <h3>OCR识别结果：</h3>
        <div id="resultText"></div>
    </div>

    <!-- 加载Tesseract.js -->
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>

    <script>
        let canvas, ctx;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            canvas = document.getElementById('testCanvas');
            ctx = canvas.getContext('2d');
            generateImage();
        });

        function generateImage() {
            const text = document.getElementById('textInput').value;
            const fontSize = document.getElementById('fontSize').value;
            const fontFamily = document.getElementById('fontFamily').value;

            // 清空画布
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 设置文字样式
            ctx.fillStyle = 'black';
            ctx.font = `${fontSize}px ${fontFamily}`;
            ctx.textAlign = 'left';
            ctx.textBaseline = 'top';

            // 绘制文字
            const lines = text.split('\n');
            const lineHeight = parseInt(fontSize) * 1.2;
            
            lines.forEach((line, index) => {
                ctx.fillText(line, 20, 20 + index * lineHeight);
            });

            // 添加一些装饰
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
        }

        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'test-image.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        async function testOCR() {
            const resultDiv = document.getElementById('ocrResult');
            const resultText = document.getElementById('resultText');
            
            try {
                resultText.innerHTML = '正在识别文字...';
                resultDiv.style.display = 'block';

                // 使用Tesseract.js进行OCR识别
                const { data } = await Tesseract.recognize(canvas, 'eng+chi_sim', {
                    logger: m => console.log(m)
                });

                let html = '<h4>识别结果：</h4>';
                html += `<p><strong>完整文本：</strong> ${data.text}</p>`;
                html += `<p><strong>置信度：</strong> ${data.confidence.toFixed(2)}%</p>`;
                
                if (data.words && data.words.length > 0) {
                    html += '<h4>单词详情：</h4>';
                    data.words.forEach((word, index) => {
                        html += `<p>${index + 1}. "${word.text}" (置信度: ${word.confidence.toFixed(1)}%)</p>`;
                    });
                }

                resultText.innerHTML = html;

            } catch (error) {
                console.error('OCR识别失败:', error);
                resultText.innerHTML = `<p style="color: red;">OCR识别失败: ${error.message}</p>`;
            }
        }

        // 添加一些预设文本
        function setPresetText(text) {
            document.getElementById('textInput').value = text;
            generateImage();
        }

        // 添加预设按钮
        document.addEventListener('DOMContentLoaded', function() {
            const controlsDiv = document.querySelector('.controls');
            const presetDiv = document.createElement('div');
            presetDiv.innerHTML = `
                <h3>预设文本：</h3>
                <button class="btn" onclick="setPresetText('Hello World')">英文</button>
                <button class="btn" onclick="setPresetText('你好世界')">中文</button>
                <button class="btn" onclick="setPresetText('Hello 世界\\n混合文字测试')">中英混合</button>
                <button class="btn" onclick="setPresetText('1234567890\\nABCDEFG\\n数字字母')">数字字母</button>
            `;
            controlsDiv.appendChild(presetDiv);
        });
    </script>
</body>
</html>
