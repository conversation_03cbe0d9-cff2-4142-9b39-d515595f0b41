<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试页面</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>按钮功能测试</h1>
            <p class="subtitle">测试各个按钮的点击事件</p>
        </div>

        <div class="main-content">
            <!-- 基础按钮测试 -->
            <div class="test-section">
                <h2>基础按钮测试</h2>
                <button id="testButton1" class="btn btn-primary">测试按钮1</button>
                <button id="testButton2" class="btn btn-secondary">测试按钮2</button>
                <div id="testResult1" class="test-result">等待点击...</div>
            </div>

            <!-- OCR按钮测试 -->
            <div class="test-section">
                <h2>OCR按钮测试</h2>
                <button id="extractTextOnly" class="btn btn-primary">提取文字</button>
                <button id="extractTextWithLayer" class="btn btn-secondary">文字分层</button>
                <div id="testResult2" class="test-result">等待点击...</div>
            </div>

            <!-- 背景分离按钮测试 -->
            <div class="test-section">
                <h2>背景分离按钮测试</h2>
                <button id="removeBackground" class="btn btn-warning">删除背景</button>
                <button id="separateBackground" class="btn btn-info">背景分离</button>
                <div id="testResult3" class="test-result">等待点击...</div>
            </div>

            <!-- 设置按钮测试 -->
            <div class="test-section">
                <h2>设置按钮测试</h2>
                <button id="openSettings" class="btn btn-settings">OCR设置</button>
                <div id="testResult4" class="test-result">等待点击...</div>
            </div>

            <!-- 控制台日志 -->
            <div class="test-section">
                <h2>控制台日志</h2>
                <div id="consoleLog" class="test-result" style="height: 200px; overflow-y: auto;">
                    控制台日志将显示在这里...
                </div>
                <button id="clearLog" class="btn btn-secondary">清空日志</button>
            </div>
        </div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.className = type === 'error' ? 'error' : (type === 'warn' ? 'warning' : '');
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        // 基础按钮测试
        document.getElementById('testButton1').addEventListener('click', function() {
            document.getElementById('testResult1').innerHTML = '<span class="success">测试按钮1点击成功！</span>';
            console.log('测试按钮1被点击');
        });

        document.getElementById('testButton2').addEventListener('click', function() {
            document.getElementById('testResult1').innerHTML = '<span class="success">测试按钮2点击成功！</span>';
            console.log('测试按钮2被点击');
        });

        // 清空日志按钮
        document.getElementById('clearLog').addEventListener('click', function() {
            document.getElementById('consoleLog').innerHTML = '控制台日志将显示在这里...';
        });

        // 模拟OCR按钮事件
        document.getElementById('extractTextOnly').addEventListener('click', function() {
            document.getElementById('testResult2').innerHTML = '<span class="success">提取文字按钮点击成功！</span>';
            console.log('提取文字按钮被点击');
        });

        document.getElementById('extractTextWithLayer').addEventListener('click', function() {
            document.getElementById('testResult2').innerHTML = '<span class="success">文字分层按钮点击成功！</span>';
            console.log('文字分层按钮被点击');
        });

        // 模拟背景分离按钮事件
        document.getElementById('removeBackground').addEventListener('click', function() {
            document.getElementById('testResult3').innerHTML = '<span class="success">删除背景按钮点击成功！</span>';
            console.log('删除背景按钮被点击');
        });

        document.getElementById('separateBackground').addEventListener('click', function() {
            document.getElementById('testResult3').innerHTML = '<span class="success">背景分离按钮点击成功！</span>';
            console.log('背景分离按钮被点击');
        });

        // 模拟设置按钮事件
        document.getElementById('openSettings').addEventListener('click', function() {
            document.getElementById('testResult4').innerHTML = '<span class="success">设置按钮点击成功！</span>';
            console.log('设置按钮被点击');
        });

        console.log('测试页面已加载完成');
    </script>
</body>
</html>
