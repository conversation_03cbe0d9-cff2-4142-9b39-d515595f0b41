/**
 * 简化的CEP接口
 * 用于与Photoshop主机应用程序通信
 */
class CSInterface {
    constructor() {
        this.hostEnvironment = null;
        this.init();
    }

    init() {
        // 检测CEP环境
        if (typeof window.__adobe_cep__ !== 'undefined') {
            this.hostEnvironment = window.__adobe_cep__.getHostEnvironment();
        } else {
            // 模拟CEP环境用于测试
            this.hostEnvironment = {
                appName: "PHXS",
                appVersion: "24.0.0"
            };
        }
    }

    /**
     * 执行ExtendScript代码
     */
    evalScript(script, callback) {
        try {
            if (typeof window.__adobe_cep__ !== 'undefined') {
                window.__adobe_cep__.evalScript(script, callback);
            } else {
                // 模拟执行
                console.log('模拟执行脚本:', script);
                if (callback) {
                    setTimeout(() => {
                        callback('模拟结果');
                    }, 100);
                }
            }
        } catch (error) {
            console.error('执行脚本失败:', error);
            if (callback) {
                callback(null, error);
            }
        }
    }

    /**
     * 获取主机环境信息
     */
    getHostEnvironment() {
        return this.hostEnvironment;
    }

    /**
     * 获取应用程序名称
     */
    getApplicationID() {
        return this.hostEnvironment ? this.hostEnvironment.appName : 'PHXS';
    }

    /**
     * 获取扩展ID
     */
    getExtensionID() {
        return 'com.ps.imagetotext.panel';
    }

    /**
     * 打开URL
     */
    openURLInDefaultBrowser(url) {
        if (typeof window.__adobe_cep__ !== 'undefined') {
            window.__adobe_cep__.util.openURLInDefaultBrowser(url);
        } else {
            window.open(url, '_blank');
        }
    }

    /**
     * 获取系统路径
     */
    getSystemPath(pathType) {
        if (typeof window.__adobe_cep__ !== 'undefined') {
            return window.__adobe_cep__.fs.getSystemPath(pathType);
        } else {
            // 返回模拟路径
            switch (pathType) {
                case 'userData':
                    return 'C:\\Users\\<USER>\\AppData\\Roaming';
                case 'commonFiles':
                    return 'C:\\Program Files\\Common Files';
                default:
                    return 'C:\\';
            }
        }
    }

    /**
     * 调用Photoshop函数的便捷方法
     */
    callPhotoshopFunction(functionName, params, callback) {
        const script = `${functionName}(${params ? JSON.stringify(params) : ''})`;
        this.evalScript(script, callback);
    }

    /**
     * 获取选中的图层
     */
    getSelectedLayers(callback) {
        this.evalScript('getSelectedLayers()', callback);
    }

    /**
     * 创建文本图层
     */
    createTextLayer(text, options, callback) {
        const script = `createTextLayer("${text}", ${JSON.stringify(options)})`;
        this.evalScript(script, callback);
    }

    /**
     * 删除背景
     */
    removeBackground(layerId, callback) {
        const script = `removeBackground("${layerId}")`;
        this.evalScript(script, callback);
    }

    /**
     * 复制图层
     */
    duplicateLayer(layerId, newName, callback) {
        const script = `duplicateLayer("${layerId}", "${newName}")`;
        this.evalScript(script, callback);
    }

    /**
     * 创建图层蒙版
     */
    createLayerMask(layerId, callback) {
        const script = `createLayerMask("${layerId}")`;
        this.evalScript(script, callback);
    }

    /**
     * 获取文档信息
     */
    getDocumentInfo(callback) {
        this.evalScript('getDocumentInfo()', callback);
    }

    /**
     * 显示警告
     */
    showAlert(message, callback) {
        const script = `showAlert("${message}")`;
        this.evalScript(script, callback);
    }

    /**
     * 导出图层
     */
    exportLayerToFile(layerId, filePath, callback) {
        const script = `exportLayerToFile("${layerId}", "${filePath}")`;
        this.evalScript(script, callback);
    }
}

// 创建全局实例
window.csInterface = new CSInterface();

// 兼容性检查
if (typeof require === 'undefined') {
    // 在CEP环境中模拟require函数
    window.require = function(module) {
        console.log('模拟require:', module);
        return {};
    };
}

console.log('CEP接口已初始化');
console.log('主机环境:', window.csInterface.getHostEnvironment());
