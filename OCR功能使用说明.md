# OCR文字识别功能使用说明

## 问题解决方案

我已经修复了您提到的"识别不了文字，无法做到分层"的问题。主要改进包括：

### 1. 集成真实OCR引擎

- **Tesseract.js**：集成了开源OCR引擎，支持多语言识别
- **OCR.space API**：提供在线OCR服务作为备选
- **本地图像处理**：添加了基础的文字区域检测

### 2. 支持的OCR服务

#### 免费服务
- **Tesseract.js**：本地运行，支持中文、英文、日文、韩文等
- **OCR.space**：免费在线API，每月500次免费调用

#### 付费服务
- **百度OCR**：需要API密钥，识别精度高
- **Google Vision**：需要API密钥，支持多种语言

### 3. 功能特点

- ✅ **真实文字识别**：不再是模拟数据
- ✅ **多语言支持**：中文、英文、日文、韩文、俄文
- ✅ **位置信息**：获取文字的精确位置和边界框
- ✅ **置信度评估**：每个识别结果都有置信度分数
- ✅ **自动分层**：根据识别结果创建对应的文字图层

## 使用方法

### 1. 在浏览器中测试

#### 测试OCR功能
1. 打开 `ocr-test.html` 页面
2. 上传包含文字的图片
3. 选择OCR服务（推荐Tesseract.js）
4. 点击"开始识别"

#### 创建测试图片
1. 打开 `create-test-image.html` 页面
2. 输入要测试的文字
3. 调整字体和大小
4. 点击"生成图片"和"测试OCR"

### 2. 在Photoshop中使用

#### 安装插件
1. 运行 `install-test.bat` 脚本
2. 启动Photoshop
3. 通过 `窗口 > 扩展功能 > 图转层` 打开插件

#### 使用步骤
1. 在Photoshop中打开包含文字的图片
2. 选择要处理的图层
3. 在插件中点击"提取文字"或"文字分层"
4. 等待处理完成

### 3. 配置OCR设置

#### 免费使用
- 默认使用Tesseract.js，无需配置
- 支持自动语言检测

#### 使用付费API
1. 点击"OCR设置"按钮
2. 选择服务提供商（百度OCR/Google Vision）
3. 输入API密钥
4. 选择识别语言
5. 保存设置

## 技术实现

### OCR引擎集成

```javascript
// 使用Tesseract.js进行识别
const { data } = await Tesseract.recognize(image, 'eng+chi_sim', {
    logger: m => console.log('Tesseract:', m)
});

// 处理识别结果
const results = data.words.map(word => ({
    text: word.text,
    confidence: word.confidence / 100,
    bbox: {
        x: word.bbox.x0,
        y: word.bbox.y0,
        width: word.bbox.x1 - word.bbox.x0,
        height: word.bbox.y1 - word.bbox.y0
    }
}));
```

### 图层处理

```javascript
// 获取图层Canvas表示
const canvas = await psAPI.getLayerAsCanvas(layer);

// 进行OCR识别
const ocrResult = await ocrService.recognizeText(canvas);

// 创建文字图层
for (const textResult of ocrResult.results) {
    await createTextLayerFromOCR(textResult, layerBounds);
}
```

## 故障排除

### 常见问题

1. **识别精度低**
   - 确保图片清晰，文字对比度高
   - 选择合适的语言设置
   - 尝试不同的OCR服务

2. **无法识别中文**
   - 确保选择了中文语言包
   - Tesseract.js会自动下载中文模型

3. **处理速度慢**
   - Tesseract.js首次使用需要下载模型
   - 大图片处理时间较长
   - 可以考虑使用在线API

4. **在Photoshop中无法使用**
   - 检查CEP调试模式是否启用
   - 确保Photoshop版本支持（2021+）
   - 查看浏览器控制台的错误信息

### 调试方法

1. **使用调试页面**
   - 打开 `debug.html` 查看详细日志
   - 检查事件绑定和错误信息

2. **浏览器测试**
   - 先在浏览器中测试OCR功能
   - 确认识别效果后再在PS中使用

3. **查看控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误和日志

## 性能优化

### 提高识别速度
- 裁剪图片到文字区域
- 降低图片分辨率（保持文字清晰）
- 使用在线API服务

### 提高识别精度
- 确保文字清晰、对比度高
- 选择正确的语言设置
- 预处理图片（去噪、二值化）

## 下一步改进

1. **图像预处理**：添加去噪、二值化等预处理功能
2. **批量处理**：支持同时处理多个图层
3. **结果编辑**：允许手动修正识别结果
4. **模板功能**：保存常用的OCR设置
5. **性能优化**：缓存OCR模型，提高处理速度

现在您可以真正识别图片中的文字并实现自动分层功能了！
