@echo off
chcp 65001 >nul
echo ========================================
echo 图转层 - PS插件安装程序
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 管理员权限检查通过
) else (
    echo [✗] 需要管理员权限运行此安装程序
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 查找Photoshop安装目录
echo [*] 正在查找Photoshop安装目录...

set "PS_PATH="
set "EXTENSIONS_PATH="

:: 检查常见的Photoshop安装路径
for %%v in (2025 2024 2023 2022 2021) do (
    if exist "C:\Program Files\Adobe\Adobe Photoshop %%v\Plug-ins\Extensions\" (
        set "PS_PATH=C:\Program Files\Adobe\Adobe Photoshop %%v"
        set "EXTENSIONS_PATH=C:\Program Files\Adobe\Adobe Photoshop %%v\Plug-ins\Extensions"
        echo [✓] 找到Photoshop %%v: !PS_PATH!
        goto :found
    )
)

:: 检查CEP扩展目录
set "CEP_PATH=%APPDATA%\Adobe\CEP\extensions"
if exist "%CEP_PATH%" (
    set "EXTENSIONS_PATH=%CEP_PATH%"
    echo [✓] 找到CEP扩展目录: !EXTENSIONS_PATH!
    goto :found
)

:: 如果没找到，让用户手动输入
:notfound
echo [✗] 未找到Photoshop安装目录
echo.
echo 请手动输入Photoshop的Extensions目录路径：
echo 例如: C:\Program Files\Adobe\Adobe Photoshop 2024\Plug-ins\Extensions
set /p "EXTENSIONS_PATH=请输入路径: "

if not exist "%EXTENSIONS_PATH%" (
    echo [✗] 路径不存在，请检查后重试
    goto :notfound
)

:found
echo [✓] 扩展目录: %EXTENSIONS_PATH%
echo.

:: 创建插件目录
set "PLUGIN_DIR=%EXTENSIONS_PATH%\图转层"
echo [*] 创建插件目录: %PLUGIN_DIR%

if exist "%PLUGIN_DIR%" (
    echo [!] 插件目录已存在，将覆盖现有文件
    rmdir /s /q "%PLUGIN_DIR%" 2>nul
)

mkdir "%PLUGIN_DIR%" 2>nul
if %errorLevel% neq 0 (
    echo [✗] 创建目录失败，请检查权限
    pause
    exit /b 1
)

:: 复制插件文件
echo [*] 复制插件文件...

xcopy /E /I /Y "manifest.json" "%PLUGIN_DIR%\" >nul
xcopy /E /I /Y "index.html" "%PLUGIN_DIR%\" >nul
xcopy /E /I /Y "package.json" "%PLUGIN_DIR%\" >nul
xcopy /E /I /Y "css" "%PLUGIN_DIR%\css\" >nul
xcopy /E /I /Y "js" "%PLUGIN_DIR%\js\" >nul
xcopy /E /I /Y "icons" "%PLUGIN_DIR%\icons\" >nul

if %errorLevel% neq 0 (
    echo [✗] 文件复制失败
    pause
    exit /b 1
)

echo [✓] 插件文件复制完成
echo.

:: 检查Photoshop是否正在运行
echo [*] 检查Photoshop进程...
tasklist /FI "IMAGENAME eq Photoshop.exe" 2>NUL | find /I /N "Photoshop.exe" >nul
if %errorLevel% == 0 (
    echo [!] 检测到Photoshop正在运行
    echo [!] 请关闭Photoshop后重新启动以加载插件
) else (
    echo [✓] Photoshop未运行
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. 启动Adobe Photoshop
echo 2. 菜单栏选择: 窗口 ^> 扩展功能 ^> 图转层
echo 3. 开始使用插件功能
echo.
echo 功能说明：
echo • 左键：仅提取文字
echo • 右键：提取文字并分层
echo • 中键：打开设置
echo.
echo 如有问题，请查看README.md文档
echo.
pause
