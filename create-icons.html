<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-preview {
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>图转层插件图标生成器</h1>
    <p>点击下面的按钮生成并下载插件所需的图标文件</p>
    
    <div class="icon-container">
        <div class="icon-preview">
            <h3>浅色主题图标</h3>
            <canvas id="lightIcon" width="23" height="23"></canvas>
            <br>
            <button onclick="generateLightIcon()">生成浅色图标</button>
            <button onclick="downloadIcon('lightIcon', 'icon-light.png')">下载</button>
        </div>
        
        <div class="icon-preview">
            <h3>深色主题图标</h3>
            <canvas id="darkIcon" width="23" height="23"></canvas>
            <br>
            <button onclick="generateDarkIcon()">生成深色图标</button>
            <button onclick="downloadIcon('darkIcon', 'icon-dark.png')">下载</button>
        </div>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin-top: 20px;">
        <h3>使用说明：</h3>
        <ol>
            <li>点击"生成浅色图标"和"生成深色图标"按钮</li>
            <li>点击对应的"下载"按钮保存图标文件</li>
            <li>将下载的图标文件放入插件的 <code>icons/</code> 目录</li>
            <li>确保文件名为 <code>icon-light.png</code> 和 <code>icon-dark.png</code></li>
        </ol>
    </div>

    <script>
        function generateLightIcon() {
            const canvas = document.getElementById('lightIcon');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 23, 23);
            
            // 背景圆形
            ctx.fillStyle = '#4A90E2';
            ctx.beginPath();
            ctx.arc(11.5, 11.5, 10.5, 0, 2 * Math.PI);
            ctx.fill();
            
            // 边框
            ctx.strokeStyle = '#2E5C8A';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // 文档背景
            ctx.fillStyle = 'white';
            ctx.fillRect(6, 4, 11, 13);
            
            // 文档边框
            ctx.strokeStyle = '#2E5C8A';
            ctx.lineWidth = 0.5;
            ctx.strokeRect(6, 4, 11, 13);
            
            // 文字线条
            ctx.strokeStyle = '#2E5C8A';
            ctx.lineWidth = 0.8;
            
            // 第一行
            ctx.beginPath();
            ctx.moveTo(8, 7);
            ctx.lineTo(15, 7);
            ctx.stroke();
            
            // 第二行
            ctx.beginPath();
            ctx.moveTo(8, 9);
            ctx.lineTo(13, 9);
            ctx.stroke();
            
            // 第三行
            ctx.beginPath();
            ctx.moveTo(8, 11);
            ctx.lineTo(15, 11);
            ctx.stroke();
            
            // 第四行
            ctx.beginPath();
            ctx.moveTo(8, 13);
            ctx.lineTo(12, 13);
            ctx.stroke();
            
            // 图层分离指示器
            ctx.fillStyle = '#E74C3C';
            ctx.globalAlpha = 0.8;
            ctx.fillRect(14, 15, 6, 4);
            
            ctx.fillStyle = 'white';
            ctx.globalAlpha = 1;
            ctx.fillRect(15, 16, 4, 2);
            
            // OCR识别指示器
            ctx.fillStyle = '#27AE60';
            ctx.globalAlpha = 0.9;
            ctx.beginPath();
            ctx.arc(5, 18, 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 字母A
            ctx.fillStyle = 'white';
            ctx.globalAlpha = 1;
            ctx.font = 'bold 2.5px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('A', 5, 19.5);
        }
        
        function generateDarkIcon() {
            const canvas = document.getElementById('darkIcon');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 23, 23);
            
            // 背景圆形（深色主题）
            ctx.fillStyle = '#6C7B7F';
            ctx.beginPath();
            ctx.arc(11.5, 11.5, 10.5, 0, 2 * Math.PI);
            ctx.fill();
            
            // 边框
            ctx.strokeStyle = '#4A5568';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // 文档背景（深色主题）
            ctx.fillStyle = '#2D3748';
            ctx.fillRect(6, 4, 11, 13);
            
            // 文档边框
            ctx.strokeStyle = '#A0AEC0';
            ctx.lineWidth = 0.5;
            ctx.strokeRect(6, 4, 11, 13);
            
            // 文字线条（浅色）
            ctx.strokeStyle = '#E2E8F0';
            ctx.lineWidth = 0.8;
            
            // 第一行
            ctx.beginPath();
            ctx.moveTo(8, 7);
            ctx.lineTo(15, 7);
            ctx.stroke();
            
            // 第二行
            ctx.beginPath();
            ctx.moveTo(8, 9);
            ctx.lineTo(13, 9);
            ctx.stroke();
            
            // 第三行
            ctx.beginPath();
            ctx.moveTo(8, 11);
            ctx.lineTo(15, 11);
            ctx.stroke();
            
            // 第四行
            ctx.beginPath();
            ctx.moveTo(8, 13);
            ctx.lineTo(12, 13);
            ctx.stroke();
            
            // 图层分离指示器
            ctx.fillStyle = '#F56565';
            ctx.globalAlpha = 0.8;
            ctx.fillRect(14, 15, 6, 4);
            
            ctx.fillStyle = '#2D3748';
            ctx.globalAlpha = 1;
            ctx.fillRect(15, 16, 4, 2);
            
            // OCR识别指示器
            ctx.fillStyle = '#48BB78';
            ctx.globalAlpha = 0.9;
            ctx.beginPath();
            ctx.arc(5, 18, 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 字母A
            ctx.fillStyle = 'white';
            ctx.globalAlpha = 1;
            ctx.font = 'bold 2.5px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('A', 5, 19.5);
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateLightIcon();
            generateDarkIcon();
        };
    </script>
</body>
</html>
