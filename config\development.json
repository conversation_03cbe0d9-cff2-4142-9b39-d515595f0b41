{"development": {"debug": true, "logLevel": "verbose", "mockOCR": true, "mockBackgroundRemoval": true, "testMode": true}, "ocr": {"defaultProvider": "free", "timeout": 30000, "retryAttempts": 3, "supportedLanguages": ["auto", "zh", "en", "ja", "ko", "ru", "fr", "de", "es"], "providers": {"free": {"name": "免费OCR", "requiresApiKey": false, "maxImageSize": "5MB"}, "baidu": {"name": "百度OCR", "requiresApiKey": true, "apiUrl": "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic", "maxImageSize": "4MB"}, "google": {"name": "Google Vision", "requiresApiKey": true, "apiUrl": "https://vision.googleapis.com/v1/images:annotate", "maxImageSize": "20MB"}, "ocrspace": {"name": "OCR.space", "requiresApiKey": false, "apiUrl": "https://api.ocr.space/parse/image", "maxImageSize": "1MB", "freeApiKey": "helloworld"}}}, "backgroundRemoval": {"defaultTolerance": 32, "featherRadius": 2, "expandSelection": 2, "autoDetectBackground": true, "preserveTransparency": true}, "ui": {"theme": "auto", "language": "zh-CN", "showTooltips": true, "animationDuration": 300, "autoHideProgress": 3000}, "performance": {"maxConcurrentOperations": 1, "tempFileCleanup": true, "memoryLimit": "512MB", "processingTimeout": 60000}, "shortcuts": {"extractText": "Ctrl+Shift+T", "extractTextWithLayer": "Ctrl+Shift+L", "removeBackground": "Ctrl+Shift+B", "separateBackground": "Ctrl+Shift+S", "openSettings": "Ctrl+Shift+O"}, "mockData": {"ocrResults": [{"text": "示例文字内容", "confidence": 0.95, "bbox": {"x": 100, "y": 50, "width": 120, "height": 30}, "fontSize": 16, "color": {"r": 0, "g": 0, "b": 0}, "font": "Microsoft YaHei"}, {"text": "Sample English Text", "confidence": 0.92, "bbox": {"x": 100, "y": 100, "width": 180, "height": 25}, "fontSize": 14, "color": {"r": 51, "g": 51, "b": 51}, "font": "<PERSON><PERSON>"}, {"text": "サンプルテキスト", "confidence": 0.88, "bbox": {"x": 100, "y": 150, "width": 140, "height": 28}, "fontSize": 15, "color": {"r": 0, "g": 0, "b": 0}, "font": "MS Gothic"}]}}